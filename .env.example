# 应用配置
VITE_APP_TITLE=Team AI 2.0
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=基于Vue3和Electron的跨平台应用框架

# API 配置
VITE_API_BASE_URL=http://localhost:8080/api
VITE_API_TIMEOUT=30000
VITE_API_RETRY_COUNT=3

# 构建配置
VITE_PUBLIC_PATH=/
VITE_BUILD_TARGET=web
VITE_LEGACY=false

# 开发配置
VITE_DEV=true
VITE_MOCK=true
VITE_DEV_TOOLS=true

# 生产配置
VITE_PROD_COMPRESS=true
VITE_PROD_COMPRESS_TYPE=gzip
VITE_PROD_SOURCE_MAP=false
VITE_PROD_REMOVE_CONSOLE=true

# 安全配置
VITE_CORS=true
VITE_CORS_ORIGIN=*
VITE_SECURITY_HEADERS=true

# 分析配置
VITE_BUNDLE_ANALYZER=false
VITE_BUNDLE_ANALYZER_PORT=8888

# PWA 配置
VITE_PWA=true
VITE_PWA_NAME=Team AI 2.0
VITE_PWA_SHORT_NAME=TeamAI
VITE_PWA_THEME_COLOR=#1890ff
VITE_PWA_BACKGROUND_COLOR=#ffffff

# 桌面端配置
VITE_DESKTOP=true
VITE_DESKTOP_ICON=resources/icon.png
VITE_DESKTOP_WIDTH=1200
VITE_DESKTOP_HEIGHT=800
VITE_DESKTOP_MIN_WIDTH=800
VITE_DESKTOP_MIN_HEIGHT=600

# 功能开关
VITE_ENABLE_STORAGE=true
VITE_ENABLE_NETWORK=true
VITE_ENABLE_NOTIFICATION=true
VITE_ENABLE_THEME=true
VITE_ENABLE_I18N=true
VITE_ENABLE_PWA=true
VITE_ENABLE_ANALYTICS=false

# 第三方服务
VITE_GA_TRACKING_ID=
VITE_SENTRY_DSN=
VITE_CRASH_REPORTING=false