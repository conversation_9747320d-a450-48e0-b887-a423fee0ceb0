# Electron Builder配置文件

appId: com.teamai.app
productName: Team AI 2.0
copyright: Copyright © 2025 Team AI

directories:
  output: dist/desktop
  buildResources: build

files:
  - dist/desktop/**/*
  - build/package.json
  - node_modules/**/*
  - "!node_modules/.pnpm/**/*"
  - "!node_modules/**/test/**/*"
  - "!node_modules/**/tests/**/*"
  - "!node_modules/**/*.md"
  - "!node_modules/**/*.txt"
  - "!node_modules/**/LICENSE*"
  - "!node_modules/**/CHANGELOG*"
  - "!node_modules/**/README*"
  - "!node_modules/**/.github/**/*"
  - "!node_modules/**/docs/**/*"
  - "!node_modules/**/example/**/*"
  - "!node_modules/**/examples/**/*"
  - "!node_modules/**/demo/**/*"
  - "!node_modules/**/demos/**/*"
  - "!node_modules/**/*.d.ts"
  - "!node_modules/**/*.map"
  - "!src/**/*"
  - "!.git/**/*"
  - "!.vscode/**/*"
  - "!.idea/**/*"
  - "!*.md"
  - "!*.txt"
  - "!*.log"
  - "!.env*"
  - "!.eslint*"
  - "!.prettier*"
  - "!tsconfig*"
  - "!vite*"
  - "!electron*"
  - "!pnpm*"

extraResources:
  - from: 'build/icons/'
    to: 'icons/'
    filter: ["**/*"]
mac:
  category: public.app-category.productivity
  target:
    - target: dmg
      arch:
        - x64
        - arm64
  hardenedRuntime: true
  entitlements: resources/entitlements.mac.plist
  entitlementsInherit: resources/entitlements.mac.plist
win:
  target:
    - target: nsis
      arch:
        - x64
        - ia32
  icon: build/icons/icon.ico
  requestedExecutionLevel: asInvoker
  artifactName: "${productName}-${version}-${arch}.${ext}"
linux:
  target:
    - target: AppImage
      arch:
        - x64
  category: Development
nsis:
  oneClick: false
  allowElevation: true
  allowToChangeInstallationDirectory: true
  installerIcon: build/icons/icon.ico
  uninstallerIcon: build/icons/icon.ico
  installerHeaderIcon: build/icons/icon.ico
  deleteAppDataOnUninstall: true
  createDesktopShortcut: true
  createStartMenuShortcut: true
  shortcutName: "Team AI 2.0"
publish:
  provider: github
  owner: team-ai
  repo: team-ai-2.0