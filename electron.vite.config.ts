/**
 * Electron Vite配置
 */

import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import { resolve } from 'path'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  main: {
    // 主进程配置
    build: {
      lib: {
        entry: resolve(__dirname, 'src/desktop/main/index.ts'),
        fileName: 'index',
        formats: ['cjs']
      },
      rollupOptions: {
        external: ['electron', ...Object.keys(require('./package.json').dependencies || {})]
      }
    },
    plugins: [externalizeDepsPlugin()],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src/desktop/main'),
        '@common': resolve(__dirname, 'src/common/src'),
        '@core': resolve(__dirname, 'src/core/src')
      }
    }
  },
  preload: {
    // 预加载脚本配置
    build: {
      lib: {
        entry: resolve(__dirname, 'src/desktop/preload/index.ts'),
        fileName: 'index',
        formats: ['cjs']
      }
    },
    plugins: [externalizeDepsPlugin()],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src/desktop/preload'),
        '@common': resolve(__dirname, 'src/common/src'),
        '@core': resolve(__dirname, 'src/core/src')
      }
    }
  },
  renderer: {
    // 渲染进程配置
    root: 'src/desktop/renderer',
    build: {
      outDir: '../../dist/desktop/renderer'
    },
    plugins: [vue()],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src/desktop/renderer/src'),
        '@common': resolve(__dirname, 'src/common/src'),
        '@core': resolve(__dirname, 'src/core/src')
      }
    },
    css: {
      preprocessorOptions: {
        less: {
          additionalData: `@import "@/assets/styles/variables.less";`,
          javascriptEnabled: true,
          modifyVars: {
            '@primary-color': '#1890ff',
            '@link-color': '#1890ff',
            '@success-color': '#52c41a',
            '@warning-color': '#faad14',
            '@error-color': '#f5222d',
            '@font-size-base': '14px',
            '@heading-color': 'rgba(0, 0, 0, 0.85)',
            '@text-color': 'rgba(0, 0, 0, 0.65)',
            '@text-color-secondary': 'rgba(0, 0, 0, 0.45)',
            '@disabled-color': 'rgba(0, 0, 0, 0.25)',
            '@border-radius-base': '4px',
            '@border-color-base': '#d9d9d9',
            '@box-shadow-base': '0 2px 8px rgba(0, 0, 0, 0.15)'
          }
        }
      }
    },
    define: {
      __DESKTOP__: true,
      __WEB__: false
    }
  }
})