{"name": "team-ai-2.0", "version": "1.0.0", "description": "Team AI 2.0", "main": "dist/desktop/main/index.js", "engines": {"pnpm": ">=9.0.0"}, "scripts": {"dev:desktop": "pnpm build:common && concurrently \"pnpm --filter desktop dev\"", "dev:web": "pnpm build:common && concurrently \"pnpm --filter web dev\"", "build": "pnpm build:common && pnpm build:web && pnpm build:installer", "build:desktop": "pnpm build:common && pnpm --filter desktop build", "dist:desktop": "pnpm build:desktop && node -e \"const fs=require('fs'),path=require('path');const dir='dist/desktop';if(fs.existsSync(dir)){fs.readdirSync(dir).forEach(f=>{if(f.endsWith('.exe')||f.endsWith('.blockmap')||f.startsWith('builder-')||f==='latest.yml'||f.startsWith('win-'))fs.rmSync(path.join(dir,f),{recursive:true,force:true})})}\" && electron-builder", "dist:desktop:dir": "pnpm build:desktop && electron-builder --dir", "build:installer": "pnpm dist:desktop", "build:web": "pnpm build:common && pnpm --filter web build", "build:common": "pnpm --filter common build", "clean": "rimraf node_modules src/*/node_modules src/*/dist --glob", "typecheck": "pnpm -r run typecheck", "lint": "pnpm -r run lint", "format": "pnpm -r run format"}, "keywords": ["electron", "vue3", "vite", "typescript", "跨平台", "桌面应用", "web应用"], "author": "Team AI", "license": "MIT", "workspaces": ["src/common", "src/core", "src/web", "src/desktop"], "devDependencies": {"@types/lodash-es": "^4.17.12", "@types/node": "^20.17.17", "concurrently": "^9.1.2", "electron": "^28.3.0", "electron-builder": "^26.0.11", "lodash-es": "^4.17.21", "rimraf": "^5.0.0", "typescript": "^5.5.2", "vue-tsc": "^2.2.8"}, "packageManager": "pnpm@9.0.0", "dependencies": {"@vitejs/plugin-vue": "^6.0.1", "electron-vite": "^4.0.1", "vite": "^7.1.7"}}