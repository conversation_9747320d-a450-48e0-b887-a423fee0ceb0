/**
 * 常量定义
 */

// 应用信息
export const APP_INFO = {
  NAME: 'Team AI 2.0',
  VERSION: '1.0.0',
  DESCRIPTION: '基于Vue3和Electron的跨平台应用框架'
} as const

// 环境类型
export const ENVIRONMENT = {
  WEB: 'web',
  DESKTOP: 'desktop'
} as const

// 平台类型
export const PLATFORM = {
  WINDOWS: 'windows',
  MACOS: 'macos',
  LINUX: 'linux'
} as const

// 主题类型
export const THEME = {
  LIGHT: 'light',
  DARK: 'dark',
  SYSTEM: 'system'
} as const

// 事件类型
export const EVENTS = {
  // 应用事件
  APP_READY: 'app:ready',
  APP_QUIT: 'app:quit',
  APP_ERROR: 'app:error',

  // 窗口事件
  WINDOW_CREATED: 'window:created',
  WINDOW_CLOSED: 'window:closed',
  WINDOW_FOCUS: 'window:focus',
  WINDOW_BLUR: 'window:blur',
  WINDOW_RESIZE: 'window:resize',
  WINDOW_MOVE: 'window:move',

  // 主题事件
  THEME_CHANGED: 'theme:changed',

  // 网络事件
  NETWORK_ONLINE: 'network:online',
  NETWORK_OFFLINE: 'network:offline',

  // 存储事件
  STORAGE_CHANGED: 'storage:changed',

  // 文件事件
  FILE_SELECTED: 'file:selected',
  FILE_SAVED: 'file:saved',
  FILE_DELETED: 'file:deleted'
} as const

// 存储键
export const STORAGE_KEYS = {
  // 主题设置
  THEME: 'theme',

  // 用户设置
  USER_SETTINGS: 'user_settings',

  // 应用状态
  APP_STATE: 'app_state',

  // 缓存数据
  CACHE_DATA: 'cache_data',

  // 历史记录
  HISTORY: 'history',

  // 会话信息
  SESSION: 'session'
} as const

// HTTP状态码
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  ACCEPTED: 202,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  METHOD_NOT_ALLOWED: 405,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503
} as const

// MIME类型
export const MIME_TYPES = {
  // 文本类型
  TEXT: 'text/plain',
  HTML: 'text/html',
  CSS: 'text/css',
  JAVASCRIPT: 'text/javascript',
  JSON: 'application/json',
  XML: 'application/xml',

  // 图片类型
  PNG: 'image/png',
  JPG: 'image/jpeg',
  GIF: 'image/gif',
  SVG: 'image/svg+xml',
  WEBP: 'image/webp',

  // 音频类型
  MP3: 'audio/mpeg',
  WAV: 'audio/wav',
  OGG: 'audio/ogg',

  // 视频类型
  MP4: 'video/mp4',
  WEBM: 'video/webm',
  AVI: 'video/x-msvideo',

  // 压缩文件
  ZIP: 'application/zip',
  RAR: 'application/x-rar-compressed',
  '7Z': 'application/x-7z-compressed',

  // 其他
  PDF: 'application/pdf',
  EXCEL: 'application/vnd.ms-excel',
  WORD: 'application/msword',
  POWERPOINT: 'application/vnd.ms-powerpoint'
} as const

// 文件扩展名映射
export const FILE_EXTENSIONS = {
  // 文本文件
  '.txt': MIME_TYPES.TEXT,
  '.html': MIME_TYPES.HTML,
  '.htm': MIME_TYPES.HTML,
  '.css': MIME_TYPES.CSS,
  '.js': MIME_TYPES.JAVASCRIPT,
  '.json': MIME_TYPES.JSON,
  '.xml': MIME_TYPES.XML,

  // 图片文件
  '.png': MIME_TYPES.PNG,
  '.jpg': MIME_TYPES.JPG,
  '.jpeg': MIME_TYPES.JPG,
  '.gif': MIME_TYPES.GIF,
  '.svg': MIME_TYPES.SVG,
  '.webp': MIME_TYPES.WEBP,

  // 音频文件
  '.mp3': MIME_TYPES.MP3,
  '.wav': MIME_TYPES.WAV,
  '.ogg': MIME_TYPES.OGG,

  // 视频文件
  '.mp4': MIME_TYPES.MP4,
  '.webm': MIME_TYPES.WEBM,
  '.avi': MIME_TYPES.AVI,

  // 压缩文件
  '.zip': MIME_TYPES.ZIP,
  '.rar': MIME_TYPES.RAR,
  '.7z': MIME_TYPES['7Z'],

  // 其他
  '.pdf': MIME_TYPES.PDF,
  '.xls': MIME_TYPES.EXCEL,
  '.xlsx': MIME_TYPES.EXCEL,
  '.doc': MIME_TYPES.WORD,
  '.docx': MIME_TYPES.WORD,
  '.ppt': MIME_TYPES.POWERPOINT,
  '.pptx': MIME_TYPES.POWERPOINT
} as const

// 响应式断点
export const BREAKPOINTS = {
  XS: 0,
  SM: 576,
  MD: 768,
  LG: 992,
  XL: 1200,
  XXL: 1400
} as const

// 动画时长
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500
} as const

// Z-index层级
export const Z_INDEX = {
  MODAL: 1000,
  DROPDOWN: 900,
  TOOLTIP: 800,
  POPOVER: 700,
  NOTIFICATION: 600,
  HEADER: 500,
  SIDEBAR: 400,
  CONTENT: 300,
  BACKGROUND: 100
} as const

// 键盘按键
export const KEY_CODES = {
  ENTER: 13,
  ESCAPE: 27,
  SPACE: 32,
  TAB: 9,
  SHIFT: 16,
  CTRL: 17,
  ALT: 18,
  META: 91,

  // 方向键
  ARROW_UP: 38,
  ARROW_DOWN: 40,
  ARROW_LEFT: 37,
  ARROW_RIGHT: 39,

  // 功能键
  F1: 112,
  F2: 113,
  F3: 114,
  F4: 115,
  F5: 116,
  F6: 117,
  F7: 118,
  F8: 119,
  F9: 120,
  F10: 121,
  F11: 122,
  F12: 123
} as const

// 正则表达式
export const REGEX = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^1[3-9]\d{9}$/,
  URL: /^https?:\/\/.+/,
  IPV4: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
  IPV6: /^(?:[A-F0-9]{1,4}:){7}[A-F0-9]{1,4}$/i,
  MAC: /^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$/,
  UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
  CHINESE: /^[\u4e00-\u9fa5]+$/,
  ENGLISH: /^[A-Za-z]+$/,
  NUMBER: /^[0-9]+$/,
  ALPHANUMERIC: /^[A-Za-z0-9]+$/,
  PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&]{8,}$/
} as const