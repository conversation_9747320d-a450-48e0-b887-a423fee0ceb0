/**
 * 通用类型定义
 */

// 环境类型
export type Environment = 'web' | 'desktop'

// 平台类型
export type Platform = 'windows' | 'macos' | 'linux'

// 应用配置接口
export interface AppConfig {
  name: string
  version: string
  description: string
  environment: Environment
  platform: Platform
}

// 存储接口
export interface IStorageService {
  setItem(key: string, value: any): Promise<void>
  getItem(key: string): Promise<any>
  removeItem(key: string): Promise<void>
  clear(): Promise<void>
}

// 窗口管理接口
export interface IWindowService {
  createWindow(options: WindowOptions): Promise<WindowHandle>
  closeWindow(id: string): Promise<void>
  minimizeWindow(id: string): Promise<void>
  maximizeWindow(id: string): Promise<void>
  restoreWindow(id: string): Promise<void>
}

export interface WindowOptions {
  title?: string
  width?: number
  height?: number
  x?: number
  y?: number
  center?: boolean
  resizable?: boolean
  movable?: boolean
  minimizable?: boolean
  maximizable?: boolean
  closable?: boolean
  focusable?: boolean
  alwaysOnTop?: boolean
  fullscreen?: boolean
  fullscreenable?: boolean
  skipTaskbar?: boolean
  kiosk?: boolean
  titleBarStyle?: 'default' | 'hidden' | 'hiddenInset' | 'customButtonsOnHover'
  vibrancy?: string
  visualEffectState?: 'active' | 'inactive' | 'followWindow'
  webPreferences?: {
    nodeIntegration?: boolean
    contextIsolation?: boolean
    enableRemoteModule?: boolean
    nodeIntegrationInWorker?: boolean
    nodeIntegrationInSubFrames?: boolean
    sandbox?: boolean
    webSecurity?: boolean
    allowRunningInsecureContent?: boolean
    experimentalFeatures?: boolean
    enableBlinkFeatures?: string
    disableBlinkFeatures?: string
    images?: boolean
    javascript?: boolean
    webAudio?: boolean
    plugins?: boolean
    offscreen?: boolean
    preload?: string
  }
}

export interface WindowHandle {
  id: string
  close: () => Promise<void>
  minimize: () => Promise<void>
  maximize: () => Promise<void>
  restore: () => Promise<void>
  focus: () => Promise<void>
  blur: () => Promise<void>
  isFocused: () => Promise<boolean>
  isVisible: () => Promise<boolean>
  isMaximized: () => Promise<boolean>
  isMinimized: () => Promise<boolean>
  isFullScreen: () => Promise<boolean>
  setBounds: (bounds: Partial<{ x: number; y: number; width: number; height: number }>) => Promise<void>
  getBounds: () => Promise<{ x: number; y: number; width: number; height: number }>
}

// 通知接口
export interface INotificationService {
  show(title: string, options?: NotificationOptions): Promise<void>
  close(id: string): Promise<void>
  closeAll(): Promise<void>
}

export interface NotificationOptions {
  body?: string
  icon?: string
  image?: string
  silent?: boolean
  requireInteraction?: boolean
  actions?: NotificationAction[]
  data?: any
  tag?: string
  renotify?: boolean
  sticky?: boolean
}

export interface NotificationAction {
  action: string
  title: string
  icon?: string
}

// 文件系统接口
export interface IFileService {
  readFile(path: string): Promise<string>
  writeFile(path: string, data: string): Promise<void>
  deleteFile(path: string): Promise<void>
  fileExists(path: string): Promise<boolean>
  readFileAsBinary(path: string): Promise<ArrayBuffer>
  writeFileAsBinary(path: string, data: ArrayBuffer): Promise<void>
}

// 系统信息接口
export interface ISystemService {
  getInfo(): Promise<SystemInfo>
  getVersion(): Promise<string>
  getPlatform(): Promise<Platform>
  getEnvironment(): Promise<Environment>
}

export interface SystemInfo {
  platform: Platform
  arch: string
  version: string
  environment: Environment
  memory: {
    total: number
    free: number
  }
  cpu: {
    count: number
    model: string
  }
}

// 网络接口
export interface INetworkService {
  request(options: RequestOptions): Promise<Response>
  get(url: string, options?: RequestInit): Promise<Response>
  post(url: string, data: any, options?: RequestInit): Promise<Response>
  put(url: string, data: any, options?: RequestInit): Promise<Response>
  delete(url: string, options?: RequestInit): Promise<Response>
}

export interface RequestOptions {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  headers?: Record<string, string>
  data?: any
  timeout?: number
}

export interface Response {
  status: number
  statusText: string
  headers: Record<string, string>
  data: any
}

// 剪贴板接口
export interface IClipboardService {
  readText(): Promise<string>
  writeText(text: string): Promise<void>
  readHTML(): Promise<string>
  writeHTML(html: string): Promise<void>
  clear(): Promise<void>
}

// 主题接口
export interface IThemeService {
  getTheme(): Promise<'light' | 'dark' | 'system'>
  setTheme(theme: 'light' | 'dark' | 'system'): Promise<void>
  onThemeChange(callback: (theme: 'light' | 'dark' | 'system') => void): () => void
}

// 日志接口
export interface ILoggerService {
  debug(message: string, ...args: any[]): void
  info(message: string, ...args: any[]): void
  warn(message: string, ...args: any[]): void
  error(message: string, ...args: any[]): void
}

// 工具函数类型
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P]
}

export type DeepRequired<T> = {
  [P in keyof T]-?: T[P] extends object ? DeepRequired<T[P]> : T[P]
}

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>

export type RequiredKeys<T, K extends keyof T> = Omit<T, K> & Required<Pick<T, K>>

export type Readonly<T> = {
  readonly [P in keyof T]: T[P]
}