/**
 * 适配器模式 - 为不同环境提供统一的接口实现
 */

import type {
  IStorageService,
  IWindowService,
  INotificationService,
  IFileService,
  ISystemService,
  INetworkService,
  IClipboardService,
  IThemeService,
  ILoggerService,
  WindowOptions,
  WindowHandle,
  NotificationOptions,
  RequestOptions,
  Response,
  SystemInfo
} from 'common/src/types'

// 适配器工厂
export class AdapterFactory {
  private static instance: AdapterFactory
  private environment: 'web' | 'desktop'
  private adapters: Map<string, any> = new Map()

  private constructor() {
    this.environment = typeof window !== 'undefined' && typeof process === 'undefined' ? 'web' : 'desktop'
  }

  static getInstance(): AdapterFactory {
    if (!AdapterFactory.instance) {
      AdapterFactory.instance = new AdapterFactory()
    }
    return AdapterFactory.instance
  }

  getEnvironment(): 'web' | 'desktop' {
    return this.environment
  }

  createStorageAdapter(): IStorageService {
    if (this.adapters.has('storage')) {
      return this.adapters.get('storage')
    }

    const adapter = this.environment === 'web'
      ? new WebStorageAdapter()
      : new DesktopStorageAdapter()

    this.adapters.set('storage', adapter)
    return adapter
  }

  createWindowAdapter(): IWindowService {
    if (this.adapters.has('window')) {
      return this.adapters.get('window')
    }

    const adapter = this.environment === 'web'
      ? new WebWindowAdapter()
      : new DesktopWindowAdapter()

    this.adapters.set('window', adapter)
    return adapter
  }

  createNotificationAdapter(): INotificationService {
    if (this.adapters.has('notification')) {
      return this.adapters.get('notification')
    }

    const adapter = this.environment === 'web'
      ? new WebNotificationAdapter()
      : new DesktopNotificationAdapter()

    this.adapters.set('notification', adapter)
    return adapter
  }

  createFileAdapter(): IFileService {
    if (this.adapters.has('file')) {
      return this.adapters.get('file')
    }

    const adapter = this.environment === 'web'
      ? new WebFileAdapter()
      : new DesktopFileAdapter()

    this.adapters.set('file', adapter)
    return adapter
  }

  createSystemAdapter(): ISystemService {
    if (this.adapters.has('system')) {
      return this.adapters.get('system')
    }

    const adapter = this.environment === 'web'
      ? new WebSystemAdapter()
      : new DesktopSystemAdapter()

    this.adapters.set('system', adapter)
    return adapter
  }

  createNetworkAdapter(): INetworkService {
    if (this.adapters.has('network')) {
      return this.adapters.get('network')
    }

    const adapter = this.environment === 'web'
      ? new WebNetworkAdapter()
      : new DesktopNetworkAdapter()

    this.adapters.set('network', adapter)
    return adapter
  }

  createClipboardAdapter(): IClipboardService {
    if (this.adapters.has('clipboard')) {
      return this.adapters.get('clipboard')
    }

    const adapter = this.environment === 'web'
      ? new WebClipboardAdapter()
      : new DesktopClipboardAdapter()

    this.adapters.set('clipboard', adapter)
    return adapter
  }

  createThemeAdapter(): IThemeService {
    if (this.adapters.has('theme')) {
      return this.adapters.get('theme')
    }

    const adapter = this.environment === 'web'
      ? new WebThemeAdapter()
      : new DesktopThemeAdapter()

    this.adapters.set('theme', adapter)
    return adapter
  }

  createLoggerAdapter(): ILoggerService {
    if (this.adapters.has('logger')) {
      return this.adapters.get('logger')
    }

    const adapter = this.environment === 'web'
      ? new WebLoggerAdapter()
      : new DesktopLoggerAdapter()

    this.adapters.set('logger', adapter)
    return adapter
  }

  clearAdapters(): void {
    this.adapters.clear()
  }
}

// Web端适配器实现
class WebStorageAdapter implements IStorageService {
  async setItem(key: string, value: any): Promise<void> {
    localStorage.setItem(key, JSON.stringify(value))
  }

  async getItem(key: string): Promise<any> {
    const item = localStorage.getItem(key)
    return item ? JSON.parse(item) : null
  }

  async removeItem(key: string): Promise<void> {
    localStorage.removeItem(key)
  }

  async clear(): Promise<void> {
    localStorage.clear()
  }
}

class WebWindowAdapter implements IWindowService {
  private windows: Map<string, WebWindowHandle> = new Map()

  async createWindow(options: WindowOptions): Promise<WindowHandle> {
    const id = `window_${Date.now()}`
    const width = options.width || 800
    const height = options.height || 600

    const features = [
      `width=${width}`,
      `height=${height}`,
      'resizable=yes',
      'scrollbars=yes',
      'status=yes'
    ]

    if (options.x !== undefined && options.y !== undefined) {
      features.push(`left=${options.x}`, `top=${options.y}`)
    }

    const win = window.open('', '_blank', features.join(','))
    if (!win) {
      throw new Error('无法创建窗口')
    }

    const handle = new WebWindowHandle(id, win)
    this.windows.set(id, handle)
    return handle
  }

  async closeWindow(id: string): Promise<void> {
    const handle = this.windows.get(id)
    if (handle) {
      handle.close()
      this.windows.delete(id)
    }
  }

  async minimizeWindow(id: string): Promise<void> {
    // Web端不支持最小化操作
  }

  async maximizeWindow(id: string): Promise<void> {
    // Web端不支持最大化操作
  }

  async restoreWindow(id: string): Promise<void> {
    // Web端不支持恢复操作
  }
}

class WebWindowHandle implements WindowHandle {
  constructor(
    public id: string,
    private win: Window
  ) {}

  async close(): Promise<void> {
    this.win.close()
  }

  async minimize(): Promise<void> {
    // Web端不支持
  }

  async maximize(): Promise<void> {
    // Web端不支持
  }

  async restore(): Promise<void> {
    // Web端不支持
  }

  async focus(): Promise<void> {
    this.win.focus()
  }

  async blur(): Promise<void> {
    this.win.blur()
  }

  async isFocused(): Promise<boolean> {
    return document.hasFocus()
  }

  async isVisible(): Promise<boolean> {
    return true
  }

  async isMaximized(): Promise<boolean> {
    return false
  }

  async isMinimized(): Promise<boolean> {
    return false
  }

  async isFullScreen(): Promise<boolean> {
    return false
  }

  async setBounds(bounds: Partial<{ x: number; y: number; width: number; height: number }>): Promise<void> {
    if (bounds.width) this.win.resizeTo(bounds.width, this.win.innerHeight)
    if (bounds.height) this.win.resizeTo(this.win.innerWidth, bounds.height)
    if (bounds.x !== undefined && bounds.y !== undefined) this.win.moveTo(bounds.x, bounds.y)
  }

  async getBounds(): Promise<{ x: number; y: number; width: number; height: number }> {
    return {
      x: this.win.screenX,
      y: this.win.screenY,
      width: this.win.innerWidth,
      height: this.win.innerHeight
    }
  }
}

class WebNotificationAdapter implements INotificationService {
  private notifications: Map<string, Notification> = new Map()

  async show(title: string, options?: NotificationOptions): Promise<void> {
    if (!('Notification' in window)) {
      console.warn('此浏览器不支持桌面通知')
      return
    }

    if (Notification.permission === 'default') {
      await Notification.requestPermission()
    }

    if (Notification.permission !== 'granted') {
      console.warn('通知权限被拒绝')
      return
    }

    const notificationOptions: any = {
      body: options?.body,
      icon: options?.icon,
      silent: options?.silent,
      requireInteraction: options?.requireInteraction,
      data: options?.data,
      tag: options?.tag,
      renotify: options?.renotify
    }

    // Only add image if supported
    if (options?.image) {
      notificationOptions.image = options.image
    }

    const notification = new Notification(title, notificationOptions)

    this.notifications.set(title, notification)
  }

  async close(id: string): Promise<void> {
    const notification = this.notifications.get(id)
    if (notification) {
      notification.close()
      this.notifications.delete(id)
    }
  }

  async closeAll(): Promise<void> {
    for (const [id, notification] of this.notifications) {
      notification.close()
    }
    this.notifications.clear()
  }
}

class WebFileAdapter implements IFileService {
  async readFile(path: string): Promise<string> {
    throw new Error('Web端不支持直接读取文件系统')
  }

  async writeFile(path: string, data: string): Promise<void> {
    throw new Error('Web端不支持直接写入文件系统')
  }

  async deleteFile(path: string): Promise<void> {
    throw new Error('Web端不支持直接删除文件')
  }

  async fileExists(path: string): Promise<boolean> {
    throw new Error('Web端不支持直接检查文件存在性')
  }

  async readFileAsBinary(path: string): Promise<ArrayBuffer> {
    throw new Error('Web端不支持直接读取二进制文件')
  }

  async writeFileAsBinary(path: string, data: ArrayBuffer): Promise<void> {
    throw new Error('Web端不支持直接写入二进制文件')
  }
}

class WebSystemAdapter implements ISystemService {
  async getInfo(): Promise<SystemInfo> {
    return {
      platform: await this.getPlatform(),
      arch: '',
      version: navigator.userAgent,
      environment: 'web',
      memory: {
        total: 0,
        free: 0
      },
      cpu: {
        count: 0,
        model: ''
      }
    }
  }

  async getVersion(): Promise<string> {
    return '1.0.0'
  }

  async getPlatform(): Promise<'windows' | 'macos' | 'linux'> {
    const userAgent = navigator.userAgent.toLowerCase()
    if (userAgent.indexOf('win') > -1) return 'windows'
    if (userAgent.indexOf('mac') > -1) return 'macos'
    if (userAgent.indexOf('linux') > -1) return 'linux'
    return 'windows' // 默认返回windows而不是unknown
  }

  async getEnvironment(): Promise<'web' | 'desktop'> {
    return 'web'
  }
}

class WebNetworkAdapter implements INetworkService {
  async request(options: RequestOptions): Promise<Response> {
    const response = await fetch(options.url, {
      method: options.method || 'GET',
      headers: options.headers,
      body: options.data ? JSON.stringify(options.data) : undefined
    })

    return {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers as any,
      data: await response.text()
    }
  }

  async get(url: string, options?: RequestInit): Promise<Response> {
    return this.request({ url, method: 'GET', headers: options?.headers as any })
  }

  async post(url: string, data: any, options?: RequestInit): Promise<Response> {
    return this.request({ url, method: 'POST', data, headers: options?.headers as any })
  }

  async put(url: string, data: any, options?: RequestInit): Promise<Response> {
    return this.request({ url, method: 'PUT', data, headers: options?.headers as any })
  }

  async delete(url: string, options?: RequestInit): Promise<Response> {
    return this.request({ url, method: 'DELETE', headers: options?.headers as any })
  }
}

class WebClipboardAdapter implements IClipboardService {
  async readText(): Promise<string> {
    return await navigator.clipboard.readText()
  }

  async writeText(text: string): Promise<void> {
    await navigator.clipboard.writeText(text)
  }

  async readHTML(): Promise<string> {
    try {
      return await navigator.clipboard.readText()
    } catch {
      return ''
    }
  }

  async writeHTML(html: string): Promise<void> {
    await navigator.clipboard.writeText(html)
  }

  async clear(): Promise<void> {
    // Web端不支持清空剪贴板
  }
}

class WebThemeAdapter implements IThemeService {
  private callbacks: Set<(theme: 'light' | 'dark' | 'system') => void> = new Set()

  async getTheme(): Promise<'light' | 'dark' | 'system'> {
    const saved = localStorage.getItem('theme')
    if (saved === 'light' || saved === 'dark' || saved === 'system') {
      return saved
    }
    return 'system'
  }

  async setTheme(theme: 'light' | 'dark' | 'system'): Promise<void> {
    localStorage.setItem('theme', theme)
    this.applyTheme(theme)
    this.callbacks.forEach(callback => callback(theme))
  }

  onThemeChange(callback: (theme: 'light' | 'dark' | 'system') => void): () => void {
    this.callbacks.add(callback)
    return () => this.callbacks.delete(callback)
  }

  private applyTheme(theme: 'light' | 'dark' | 'system'): void {
    if (theme === 'system') {
      const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      document.documentElement.setAttribute('data-theme', isDark ? 'dark' : 'light')
    } else {
      document.documentElement.setAttribute('data-theme', theme)
    }
  }
}

class WebLoggerAdapter implements ILoggerService {
  debug(message: string, ...args: any[]): void {
    console.debug(`[DEBUG] ${message}`, ...args)
  }

  info(message: string, ...args: any[]): void {
    console.info(`[INFO] ${message}`, ...args)
  }

  warn(message: string, ...args: any[]): void {
    console.warn(`[WARN] ${message}`, ...args)
  }

  error(message: string, ...args: any[]): void {
    console.error(`[ERROR] ${message}`, ...args)
  }
}

// Desktop端适配器实现（占位符，实际实现在desktop模块中）
class DesktopStorageAdapter implements IStorageService {
  async setItem(key: string, value: any): Promise<void> {
    // 实际实现会在desktop模块中
    throw new Error('DesktopStorageAdapter未实现')
  }

  async getItem(key: string): Promise<any> {
    throw new Error('DesktopStorageAdapter未实现')
  }

  async removeItem(key: string): Promise<void> {
    throw new Error('DesktopStorageAdapter未实现')
  }

  async clear(): Promise<void> {
    throw new Error('DesktopStorageAdapter未实现')
  }
}

class DesktopWindowAdapter implements IWindowService {
  async createWindow(options: WindowOptions): Promise<WindowHandle> {
    throw new Error('DesktopWindowAdapter未实现')
  }

  async closeWindow(id: string): Promise<void> {
    throw new Error('DesktopWindowAdapter未实现')
  }

  async minimizeWindow(id: string): Promise<void> {
    throw new Error('DesktopWindowAdapter未实现')
  }

  async maximizeWindow(id: string): Promise<void> {
    throw new Error('DesktopWindowAdapter未实现')
  }

  async restoreWindow(id: string): Promise<void> {
    throw new Error('DesktopWindowAdapter未实现')
  }
}

class DesktopNotificationAdapter implements INotificationService {
  async show(title: string, options?: NotificationOptions): Promise<void> {
    throw new Error('DesktopNotificationAdapter未实现')
  }

  async close(id: string): Promise<void> {
    throw new Error('DesktopNotificationAdapter未实现')
  }

  async closeAll(): Promise<void> {
    throw new Error('DesktopNotificationAdapter未实现')
  }
}

class DesktopFileAdapter implements IFileService {
  async readFile(path: string): Promise<string> {
    throw new Error('DesktopFileAdapter未实现')
  }

  async writeFile(path: string, data: string): Promise<void> {
    throw new Error('DesktopFileAdapter未实现')
  }

  async deleteFile(path: string): Promise<void> {
    throw new Error('DesktopFileAdapter未实现')
  }

  async fileExists(path: string): Promise<boolean> {
    throw new Error('DesktopFileAdapter未实现')
  }

  async readFileAsBinary(path: string): Promise<ArrayBuffer> {
    throw new Error('DesktopFileAdapter未实现')
  }

  async writeFileAsBinary(path: string, data: ArrayBuffer): Promise<void> {
    throw new Error('DesktopFileAdapter未实现')
  }
}

class DesktopSystemAdapter implements ISystemService {
  async getInfo(): Promise<SystemInfo> {
    throw new Error('DesktopSystemAdapter未实现')
  }

  async getVersion(): Promise<string> {
    throw new Error('DesktopSystemAdapter未实现')
  }

  async getPlatform(): Promise<'windows' | 'macos' | 'linux'> {
    throw new Error('DesktopSystemAdapter未实现')
  }

  async getEnvironment(): Promise<'web' | 'desktop'> {
    throw new Error('DesktopSystemAdapter未实现')
  }
}

class DesktopNetworkAdapter implements INetworkService {
  async request(options: RequestOptions): Promise<Response> {
    throw new Error('DesktopNetworkAdapter未实现')
  }

  async get(url: string, options?: RequestInit): Promise<Response> {
    throw new Error('DesktopNetworkAdapter未实现')
  }

  async post(url: string, data: any, options?: RequestInit): Promise<Response> {
    throw new Error('DesktopNetworkAdapter未实现')
  }

  async put(url: string, data: any, options?: RequestInit): Promise<Response> {
    throw new Error('DesktopNetworkAdapter未实现')
  }

  async delete(url: string, options?: RequestInit): Promise<Response> {
    throw new Error('DesktopNetworkAdapter未实现')
  }
}

class DesktopClipboardAdapter implements IClipboardService {
  async readText(): Promise<string> {
    throw new Error('DesktopClipboardAdapter未实现')
  }

  async writeText(text: string): Promise<void> {
    throw new Error('DesktopClipboardAdapter未实现')
  }

  async readHTML(): Promise<string> {
    throw new Error('DesktopClipboardAdapter未实现')
  }

  async writeHTML(html: string): Promise<void> {
    throw new Error('DesktopClipboardAdapter未实现')
  }

  async clear(): Promise<void> {
    throw new Error('DesktopClipboardAdapter未实现')
  }
}

class DesktopThemeAdapter implements IThemeService {
  async getTheme(): Promise<'light' | 'dark' | 'system'> {
    throw new Error('DesktopThemeAdapter未实现')
  }

  async setTheme(theme: 'light' | 'dark' | 'system'): Promise<void> {
    throw new Error('DesktopThemeAdapter未实现')
  }

  onThemeChange(callback: (theme: 'light' | 'dark' | 'system') => void): () => void {
    throw new Error('DesktopThemeAdapter未实现')
  }
}

class DesktopLoggerAdapter implements ILoggerService {
  debug(message: string, ...args: any[]): void {
    throw new Error('DesktopLoggerAdapter未实现')
  }

  info(message: string, ...args: any[]): void {
    throw new Error('DesktopLoggerAdapter未实现')
  }

  warn(message: string, ...args: any[]): void {
    throw new Error('DesktopLoggerAdapter未实现')
  }

  error(message: string, ...args: any[]): void {
    throw new Error('DesktopLoggerAdapter未实现')
  }
}