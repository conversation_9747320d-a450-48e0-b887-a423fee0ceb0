/**
 * 核心业务逻辑层入口文件
 */

// 导出接口
export * from './interfaces'

// 导出适配器
export * from './adapters'

// 导出服务
export * from './services'

// 导出应用管理器
export { AppManager, ServiceFactory } from './services'

// 导入类用于默认导出
import { AdapterFactory } from './adapters'
import { AppManager, ServiceManager, ConfigManager, EventManager, LifecycleManager, ErrorHandler, PerformanceMonitor, StateManager, ServiceFactory } from './services'

// 版本信息
export const version = '1.0.0'

// 默认导出
export default {
  version,
  interfaces: {
    // 这里可以添加常用的接口
  },
  adapters: {
    AdapterFactory
  },
  services: {
    AppManager,
    ServiceManager,
    ConfigManager,
    EventManager,
    LifecycleManager,
    ErrorHandler,
    PerformanceMonitor,
    StateManager,
    ServiceFactory
  }
}