/**
 * 核心接口定义 - 适配器模式
 */

import type {
  IStorageService,
  IWindowService,
  INotificationService,
  IFileService,
  ISystemService,
  INetworkService,
  IClipboardService,
  IThemeService,
  ILoggerService
} from 'common/src/types'

// 重新导出通用类型接口
export type {
  IStorageService,
  IWindowService,
  INotificationService,
  IFileService,
  ISystemService,
  INetworkService,
  IClipboardService,
  IThemeService,
  ILoggerService
} from 'common/src/types'

// 应用管理器接口
export interface IAppManager {
  init(): Promise<void>
  destroy(): Promise<void>
  getInfo(): Promise<AppInfo>
  getEnvironment(): Promise<'web' | 'desktop'>
  getPlatform(): Promise<'windows' | 'macos' | 'linux'>
}

export interface AppInfo {
  name: string
  version: string
  description: string
  environment: 'web' | 'desktop'
  platform: 'windows' | 'macos' | 'linux'
}

// 服务管理器接口
export interface IServiceManager {
  getStorage(): IStorageService
  getWindow(): IWindowService
  getNotification(): INotificationService
  getFile(): IFileService
  getSystem(): ISystemService
  getNetwork(): INetworkService
  getClipboard(): IClipboardService
  getTheme(): IThemeService
  getLogger(): ILoggerService
  registerService<T>(name: string, service: T): void
  getService<T>(name: string): T
  hasService(name: string): boolean
  removeService(name: string): void
}

// 配置管理器接口
export interface IConfigManager {
  get<T>(key: string, defaultValue?: T): Promise<T>
  set<T>(key: string, value: T): Promise<void>
  remove(key: string): Promise<void>
  clear(): Promise<void>
  has(key: string): Promise<boolean>
  getAll(): Promise<Record<string, any>>
  watch(key: string, callback: (value: any) => void): () => void
}

// 事件管理器接口
export interface IEventManager {
  on(event: string, callback: Function): void
  off(event: string, callback: Function): void
  emit(event: string, ...args: any[]): void
  once(event: string, callback: Function): void
  removeAllListeners(event?: string): void
  getListenerCount(event: string): number
}

// 插件管理器接口
export interface IPluginManager {
  load(plugin: IPlugin): Promise<void>
  unload(pluginId: string): Promise<void>
  getPlugin(pluginId: string): IPlugin | null
  getAllPlugins(): IPlugin[]
  enablePlugin(pluginId: string): Promise<void>
  disablePlugin(pluginId: string): Promise<void>
  isPluginEnabled(pluginId: string): boolean
}

export interface IPlugin {
  id: string
  name: string
  version: string
  description: string
  dependencies?: string[]
  init(): Promise<void>
  destroy(): Promise<void>
  onEnable?(): Promise<void>
  onDisable?(): Promise<void>
}

// 生命周期管理器接口
export interface ILifecycleManager {
  onBeforeInit(callback: () => Promise<void>): void
  onInit(callback: () => Promise<void>): void
  onBeforeDestroy(callback: () => Promise<void>): void
  onDestroy(callback: () => Promise<void>): void
  init(): Promise<void>
  destroy(): Promise<void>
}

// 错误处理接口
export interface IErrorHandler {
  handleError(error: Error | string, context?: any): void
  addHandler(handler: (error: Error, context?: any) => void): void
  removeHandler(handler: (error: Error, context?: any) => void): void
  setGlobalHandler(): void
}

// 性能监控接口
export interface IPerformanceMonitor {
  startMeasure(name: string): void
  endMeasure(name: string): number
  measure(name: string, callback: () => void): number
  getMetrics(): PerformanceMetrics
  clearMetrics(): void
}

export interface PerformanceMetrics {
  memory: {
    used: number
    total: number
    percentage: number
  }
  cpu: {
    usage: number
  }
  timing: {
    [key: string]: number
  }
}

// 状态管理接口
export interface IStateManager {
  getState<T>(key: string): Promise<T | null>
  setState<T>(key: string, value: T): Promise<void>
  removeState(key: string): Promise<void>
  clearState(): Promise<void>
  watchState<T>(key: string, callback: (value: T | null) => void): () => void
}

// 安全管理接口
export interface ISecurityManager {
  encrypt(data: string, key: string): Promise<string>
  decrypt(encrypted: string, key: string): Promise<string>
  hash(data: string): Promise<string>
  verify(data: string, hash: string): Promise<boolean>
  generateToken(): Promise<string>
  verifyToken(token: string): Promise<boolean>
}

// 权限管理接口
export interface IPermissionManager {
  requestPermission(permission: string): Promise<boolean>
  checkPermission(permission: string): Promise<boolean>
  requestPermissions(permissions: string[]): Promise<Record<string, boolean>>
  checkPermissions(permissions: string[]): Promise<Record<string, boolean>>
  revokePermission(permission: string): Promise<void>
}

// 更新管理接口
export interface IUpdateManager {
  checkUpdate(): Promise<UpdateInfo | null>
  downloadUpdate(): Promise<void>
  installUpdate(): Promise<void>
  onUpdateAvailable(callback: (info: UpdateInfo) => void): void
  onDownloadProgress(callback: (progress: number) => void): void
  onUpdateDownloaded(callback: () => void): void
  onUpdateError(callback: (error: Error) => void): void
}

export interface UpdateInfo {
  version: string
  releaseNotes: string
  releaseDate: string
  downloadUrl: string
  size: number
  checksum: string
}

// 国际化接口
export interface II18nManager {
  setLanguage(language: string): Promise<void>
  getLanguage(): string
  getAvailableLanguages(): string[]
  translate(key: string, params?: Record<string, string>): string
  onLanguageChange(callback: (language: string) => void): void
}

// 日志管理接口
export interface ILogManager {
  debug(message: string, ...args: any[]): void
  info(message: string, ...args: any[]): void
  warn(message: string, ...args: any[]): void
  error(message: string, ...args: any[]): void
  setLevel(level: 'debug' | 'info' | 'warn' | 'error'): void
  getLevel(): string
  addTransport(transport: ILogTransport): void
  removeTransport(transport: ILogTransport): void
}

export interface ILogTransport {
  log(level: string, message: string, ...args: any[]): void
}

// 数据验证接口
export interface IValidator {
  validate<T>(data: T, schema: ValidationSchema): Promise<ValidationResult<T>>
  addSchema(name: string, schema: ValidationSchema): void
  removeSchema(name: string): void
  getSchema(name: string): ValidationSchema | null
}

export interface ValidationSchema {
  type: 'object' | 'string' | 'number' | 'boolean' | 'array'
  required?: string[]
  properties?: Record<string, ValidationSchema>
  additionalProperties?: boolean
  min?: number
  max?: number
  pattern?: string
  enum?: any[]
  items?: ValidationSchema
}

export interface ValidationResult<T> {
  valid: boolean
  data?: T
  errors: ValidationError[]
}

export interface ValidationError {
  path: string
  message: string
  value?: any
}

// 缓存管理接口
export interface ICacheManager {
  set<T>(key: string, value: T, ttl?: number): Promise<void>
  get<T>(key: string): Promise<T | null>
  has(key: string): Promise<boolean>
  remove(key: string): Promise<void>
  clear(): Promise<void>
  keys(): Promise<string[]>
  size(): Promise<number>
}

// 任务队列接口
export interface ITaskQueue {
  add<T>(task: () => Promise<T>): Promise<T>
  addPriority<T>(task: () => Promise<T>, priority: number): Promise<T>
  pause(): void
  resume(): void
  clear(): void
  getStats(): QueueStats
}

export interface QueueStats {
  pending: number
  running: number
  completed: number
  failed: number
}

// 数据同步接口
export interface ISyncManager {
  sync(data: any): Promise<void>
  getSyncStatus(): Promise<SyncStatus>
  onSyncComplete(callback: (data: any) => void): void
  onSyncError(callback: (error: Error) => void): void
  onSyncProgress(callback: (progress: number) => void): void
}

export interface SyncStatus {
  inProgress: boolean
  progress: number
  lastSync?: Date
  error?: string
}