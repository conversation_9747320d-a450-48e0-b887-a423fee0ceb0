/**
 * 核心服务管理
 */

import { AdapterFactory } from '../adapters'
import type {
  IStorageService,
  IWindowService,
  INotificationService,
  IFileService,
  ISystemService,
  INetworkService,
  IClipboardService,
  IThemeService,
  ILoggerService,
  IAppManager,
  IServiceManager,
  IConfigManager,
  IEventManager,
  ILifecycleManager,
  IErrorHandler,
  IPerformanceMonitor,
  IStateManager
} from '../interfaces'

// 应用管理器实现
export class AppManager implements IAppManager {
  private initialized = false
  private adapterFactory: AdapterFactory

  constructor() {
    this.adapterFactory = AdapterFactory.getInstance()
  }

  async init(): Promise<void> {
    if (this.initialized) {
      return
    }

    try {
      // 初始化所有适配器
      await this.adapterFactory.createStorageAdapter()
      await this.adapterFactory.createWindowAdapter()
      await this.adapterFactory.createNotificationAdapter()
      await this.adapterFactory.createFileAdapter()
      await this.adapterFactory.createSystemAdapter()
      await this.adapterFactory.createNetworkAdapter()
      await this.adapterFactory.createClipboardAdapter()
      await this.adapterFactory.createThemeAdapter()
      await this.adapterFactory.createLoggerAdapter()

      this.initialized = true
      console.log('应用初始化完成')
    } catch (error) {
      console.error('应用初始化失败:', error)
      throw error
    }
  }

  async destroy(): Promise<void> {
    if (!this.initialized) {
      return
    }

    try {
      // 清理所有适配器
      this.adapterFactory.clearAdapters()
      this.initialized = false
      console.log('应用销毁完成')
    } catch (error) {
      console.error('应用销毁失败:', error)
      throw error
    }
  }

  async getInfo() {
    return {
      name: 'Team AI 2.0',
      version: '1.0.0',
      description: '基于Vue3和Electron的跨平台应用框架',
      environment: await this.getEnvironment(),
      platform: await this.getPlatform()
    }
  }

  async getEnvironment(): Promise<'web' | 'desktop'> {
    return this.adapterFactory.getEnvironment()
  }

  async getPlatform(): Promise<'windows' | 'macos' | 'linux'> {
    const systemAdapter = await this.adapterFactory.createSystemAdapter()
    return await systemAdapter.getPlatform()
  }
}

// 服务管理器实现
export class ServiceManager implements IServiceManager {
  private services: Map<string, any> = new Map()
  private adapterFactory: AdapterFactory

  constructor() {
    this.adapterFactory = AdapterFactory.getInstance()
  }

  getStorage(): IStorageService {
    return this.adapterFactory.createStorageAdapter()
  }

  getWindow(): IWindowService {
    return this.adapterFactory.createWindowAdapter()
  }

  getNotification(): INotificationService {
    return this.adapterFactory.createNotificationAdapter()
  }

  getFile(): IFileService {
    return this.adapterFactory.createFileAdapter()
  }

  getSystem(): ISystemService {
    return this.adapterFactory.createSystemAdapter()
  }

  getNetwork(): INetworkService {
    return this.adapterFactory.createNetworkAdapter()
  }

  getClipboard(): IClipboardService {
    return this.adapterFactory.createClipboardAdapter()
  }

  getTheme(): IThemeService {
    return this.adapterFactory.createThemeAdapter()
  }

  getLogger(): ILoggerService {
    return this.adapterFactory.createLoggerAdapter()
  }

  registerService<T>(name: string, service: T): void {
    this.services.set(name, service)
  }

  getService<T>(name: string): T {
    return this.services.get(name)
  }

  hasService(name: string): boolean {
    return this.services.has(name)
  }

  removeService(name: string): void {
    this.services.delete(name)
  }
}

// 配置管理器实现
export class ConfigManager implements IConfigManager {
  private storage: IStorageService
  private watchers: Map<string, Set<(value: any) => void>> = new Map()
  private cache: Map<string, any> = new Map()

  constructor(storage: IStorageService) {
    this.storage = storage
  }

  async get<T>(key: string, defaultValue?: T): Promise<T> {
    // 先从缓存获取
    if (this.cache.has(key)) {
      return this.cache.get(key)
    }

    try {
      const value = await this.storage.getItem(key)
      const result = value !== null ? value : defaultValue
      this.cache.set(key, result)
      return result
    } catch (error) {
      console.error(`获取配置失败: ${key}`, error)
      return defaultValue as T
    }
  }

  async set<T>(key: string, value: T): Promise<void> {
    try {
      await this.storage.setItem(key, value)
      this.cache.set(key, value)

      // 通知观察者
      const watchers = this.watchers.get(key)
      if (watchers) {
        watchers.forEach(callback => callback(value))
      }
    } catch (error) {
      console.error(`设置配置失败: ${key}`, error)
      throw error
    }
  }

  async remove(key: string): Promise<void> {
    try {
      await this.storage.removeItem(key)
      this.cache.delete(key)

      // 通知观察者
      const watchers = this.watchers.get(key)
      if (watchers) {
        watchers.forEach(callback => callback(null))
      }
    } catch (error) {
      console.error(`删除配置失败: ${key}`, error)
      throw error
    }
  }

  async clear(): Promise<void> {
    try {
      await this.storage.clear()
      this.cache.clear()
      this.watchers.clear()
    } catch (error) {
      console.error('清空配置失败:', error)
      throw error
    }
  }

  async has(key: string): Promise<boolean> {
    try {
      const value = await this.storage.getItem(key)
      return value !== null
    } catch (error) {
      console.error(`检查配置存在性失败: ${key}`, error)
      return false
    }
  }

  async getAll(): Promise<Record<string, any>> {
    try {
      // 这里需要根据具体实现来获取所有配置
      return Object.fromEntries(this.cache.entries())
    } catch (error) {
      console.error('获取所有配置失败:', error)
      return {}
    }
  }

  watch(key: string, callback: (value: any) => void): () => void {
    if (!this.watchers.has(key)) {
      this.watchers.set(key, new Set())
    }
    this.watchers.get(key)!.add(callback)

    // 返回取消监听的函数
    return () => {
      const watchers = this.watchers.get(key)
      if (watchers) {
        watchers.delete(callback)
        if (watchers.size === 0) {
          this.watchers.delete(key)
        }
      }
    }
  }
}

// 事件管理器实现
export class EventManager implements IEventManager {
  private events: Map<string, Function[]> = new Map()

  on(event: string, callback: Function): void {
    if (!this.events.has(event)) {
      this.events.set(event, [])
    }
    this.events.get(event)!.push(callback)
  }

  off(event: string, callback: Function): void {
    const callbacks = this.events.get(event)
    if (callbacks) {
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  emit(event: string, ...args: any[]): void {
    const callbacks = this.events.get(event)
    if (callbacks) {
      callbacks.forEach(callback => callback(...args))
    }
  }

  once(event: string, callback: Function): void {
    const onceCallback = (...args: any[]) => {
      callback(...args)
      this.off(event, onceCallback)
    }
    this.on(event, onceCallback)
  }

  removeAllListeners(event?: string): void {
    if (event) {
      this.events.delete(event)
    } else {
      this.events.clear()
    }
  }

  getListenerCount(event: string): number {
    const callbacks = this.events.get(event)
    return callbacks ? callbacks.length : 0
  }
}

// 生命周期管理器实现
export class LifecycleManager implements ILifecycleManager {
  private beforeInitCallbacks: Array<() => Promise<void>> = []
  private initCallbacks: Array<() => Promise<void>> = []
  private beforeDestroyCallbacks: Array<() => Promise<void>> = []
  private destroyCallbacks: Array<() => Promise<void>> = []

  onBeforeInit(callback: () => Promise<void>): void {
    this.beforeInitCallbacks.push(callback)
  }

  onInit(callback: () => Promise<void>): void {
    this.initCallbacks.push(callback)
  }

  onBeforeDestroy(callback: () => Promise<void>): void {
    this.beforeDestroyCallbacks.push(callback)
  }

  onDestroy(callback: () => Promise<void>): void {
    this.destroyCallbacks.push(callback)
  }

  async init(): Promise<void> {
    // 执行初始化前的回调
    for (const callback of this.beforeInitCallbacks) {
      await callback()
    }

    // 执行初始化回调
    for (const callback of this.initCallbacks) {
      await callback()
    }
  }

  async destroy(): Promise<void> {
    // 执行销毁前的回调
    for (const callback of this.beforeDestroyCallbacks) {
      await callback()
    }

    // 执行销毁回调
    for (const callback of this.destroyCallbacks) {
      await callback()
    }
  }
}

// 错误处理器实现
export class ErrorHandler implements IErrorHandler {
  private handlers: Array<(error: Error, context?: any) => void> = []

  handleError(error: Error | string, context?: any): void {
    const err = error instanceof Error ? error : new Error(error)

    // 调用所有处理器
    for (const handler of this.handlers) {
      try {
        handler(err, context)
      } catch (handlerError) {
        console.error('错误处理器执行失败:', handlerError)
      }
    }
  }

  addHandler(handler: (error: Error, context?: any) => void): void {
    this.handlers.push(handler)
  }

  removeHandler(handler: (error: Error, context?: any) => void): void {
    const index = this.handlers.indexOf(handler)
    if (index > -1) {
      this.handlers.splice(index, 1)
    }
  }

  setGlobalHandler(): void {
    window.addEventListener('error', (event) => {
      this.handleError(event.error, { filename: event.filename, line: event.lineno, column: event.colno })
    })

    window.addEventListener('unhandledrejection', (event) => {
      this.handleError(event.reason instanceof Error ? event.reason : new Error(String(event.reason)))
    })
  }
}

// 性能监控器实现
export class PerformanceMonitor implements IPerformanceMonitor {
  private metrics: Map<string, number> = new Map()
  private startTime: Map<string, number> = new Map()

  startMeasure(name: string): void {
    this.startTime.set(name, performance.now())
  }

  endMeasure(name: string): number {
    const start = this.startTime.get(name)
    if (!start) {
      throw new Error(`未找到开始时间: ${name}`)
    }

    const duration = performance.now() - start
    this.metrics.set(name, duration)
    this.startTime.delete(name)
    return duration
  }

  measure(name: string, callback: () => void): number {
    this.startMeasure(name)
    callback()
    return this.endMeasure(name)
  }

  getMetrics() {
    return {
      memory: {
        used: (performance as any).memory ? (performance as any).memory.usedJSHeapSize : 0,
        total: (performance as any).memory ? (performance as any).memory.totalJSHeapSize : 0,
        percentage: (performance as any).memory ? ((performance as any).memory.usedJSHeapSize / (performance as any).memory.totalJSHeapSize) * 100 : 0
      },
      cpu: {
        usage: 0 // Web端无法直接获取CPU使用率
      },
      timing: Object.fromEntries(this.metrics.entries())
    }
  }

  clearMetrics(): void {
    this.metrics.clear()
    this.startTime.clear()
  }
}

// 状态管理器实现
export class StateManager implements IStateManager {
  private state: Map<string, any> = new Map()
  private watchers: Map<string, Set<(value: any) => void>> = new Map()

  async getState<T>(key: string): Promise<T | null> {
    return this.state.get(key) || null
  }

  async setState<T>(key: string, value: T): Promise<void> {
    this.state.set(key, value)

    // 通知观察者
    const watchers = this.watchers.get(key)
    if (watchers) {
      watchers.forEach(callback => callback(value))
    }
  }

  async removeState(key: string): Promise<void> {
    this.state.delete(key)

    // 通知观察者
    const watchers = this.watchers.get(key)
    if (watchers) {
      watchers.forEach(callback => callback(null))
    }
  }

  async clearState(): Promise<void> {
    this.state.clear()
    this.watchers.clear()
  }

  watchState<T>(key: string, callback: (value: T | null) => void): () => void {
    if (!this.watchers.has(key)) {
      this.watchers.set(key, new Set())
    }
    this.watchers.get(key)!.add(callback)

    // 返回取消监听的函数
    return () => {
      const watchers = this.watchers.get(key)
      if (watchers) {
        watchers.delete(callback)
        if (watchers.size === 0) {
          this.watchers.delete(key)
        }
      }
    }
  }
}

// 服务工厂
export class ServiceFactory {
  private static instance: ServiceFactory
  private serviceManager: ServiceManager
  private configManager!: ConfigManager
  private eventManager: EventManager
  private lifecycleManager: LifecycleManager
  private errorHandler: ErrorHandler
  private performanceMonitor: PerformanceMonitor
  private stateManager: StateManager

  private constructor() {
    this.eventManager = new EventManager()
    this.lifecycleManager = new LifecycleManager()
    this.errorHandler = new ErrorHandler()
    this.performanceMonitor = new PerformanceMonitor()
    this.stateManager = new StateManager()
    this.serviceManager = new ServiceManager()
  }

  static getInstance(): ServiceFactory {
    if (!ServiceFactory.instance) {
      ServiceFactory.instance = new ServiceFactory()
    }
    return ServiceFactory.instance
  }

  async init(): Promise<void> {
    // 初始化配置管理器
    const storage = await this.serviceManager.getStorage()
    this.configManager = new ConfigManager(storage)

    // 注册服务
    this.serviceManager.registerService('config', this.configManager)
    this.serviceManager.registerService('events', this.eventManager)
    this.serviceManager.registerService('lifecycle', this.lifecycleManager)
    this.serviceManager.registerService('error', this.errorHandler)
    this.serviceManager.registerService('performance', this.performanceMonitor)
    this.serviceManager.registerService('state', this.stateManager)

    // 设置全局错误处理器
    this.errorHandler.setGlobalHandler()
  }

  getServiceManager(): ServiceManager {
    return this.serviceManager
  }

  getConfigManager(): ConfigManager {
    return this.configManager
  }

  getEventManager(): EventManager {
    return this.eventManager
  }

  getLifecycleManager(): LifecycleManager {
    return this.lifecycleManager
  }

  getErrorHandler(): ErrorHandler {
    return this.errorHandler
  }

  getPerformanceMonitor(): PerformanceMonitor {
    return this.performanceMonitor
  }

  getStateManager(): StateManager {
    return this.stateManager
  }
}