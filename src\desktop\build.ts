/**
 * Desktop端构建脚本
 */

import { build } from 'vite'
import { resolve } from 'path'
import { execSync } from 'child_process'
import fs from 'fs-extra'
import { createRequire } from 'module'

const require = createRequire(import.meta.url)

// 配置
const config = {
  // 主进程配置
  main: {
    configFile: false,
    build: {
      outDir: resolve(__dirname, '../../dist/desktop/main'),
      lib: {
        entry: resolve(__dirname, 'main/index.ts'),
        name: 'main',
        fileName: 'index',
        formats: ['cjs']
      },
      rollupOptions: {
        external: ['electron', '@electron-toolkit/utils', '@electron-toolkit/preload']
      }
    }
  },
  // 预加载脚本配置
  preload: {
    configFile: false,
    build: {
      outDir: resolve(__dirname, '../../dist/desktop/preload'),
      lib: {
        entry: resolve(__dirname, 'preload/index.ts'),
        name: 'preload',
        fileName: 'index',
        formats: ['cjs']
      },
      rollupOptions: {
        external: ['electron', '@electron-toolkit/preload']
      }
    }
  },
  // 渲染进程配置
  renderer: {
    configFile: resolve(__dirname, 'vite.config.ts'),
    build: {
      outDir: resolve(__dirname, '../../dist/desktop/renderer'),
      rollupOptions: {
        input: resolve(__dirname, 'renderer/index.html')
      }
    }
  }
}

// 构建主进程
async function buildMain() {
  console.log('Building main process...')

  await build({
    ...config.main,
    mode: 'production'
  })

  console.log('Main process built successfully!')
}

// 构建预加载脚本
async function buildPreload() {
  console.log('Building preload script...')

  await build({
    ...config.preload,
    mode: 'production'
  })

  console.log('Preload script built successfully!')
}

// 构建渲染进程函数已在下面导出

// 复制资源文件
async function copyResources() {
  console.log('Copying resources...')

  const resourcesDir = resolve(__dirname, 'resources')
  const outputDir = resolve(__dirname, '../../dist/desktop/resources')

  if (await fs.pathExists(resourcesDir)) {
    await fs.copy(resourcesDir, outputDir)
    console.log('Resources copied successfully!')
  } else {
    console.log('No resources to copy')
  }
}

// 复制package.json
async function copyPackageJson() {
  console.log('Copying package.json...')

  const packageJson = await fs.readJson(resolve(__dirname, 'package.json'))
  const outputPackageJson = {
    ...packageJson,
    main: 'dist/main/index.js',
    scripts: undefined,
    devDependencies: undefined
  }

  await fs.writeJson(
    resolve(__dirname, '../../dist/desktop/package.json'),
    outputPackageJson,
    { spaces: 2 }
  )

  console.log('Package.json copied successfully!')
}

// 构建Electron应用
async function buildElectron() {
  console.log('Building Electron application...')

  try {
    // 执行electron-builder命令
    execSync('electron-builder --config electron-builder.yml', {
      cwd: resolve(__dirname, '../../'),
      stdio: 'inherit'
    })

    console.log('Electron application built successfully!')
  } catch (error) {
    console.error('Failed to build Electron application:', error)
    process.exit(1)
  }
}

// 主构建函数
async function main() {
  try {
    console.log('Starting Desktop build process...')

    // 清理输出目录
    await fs.remove(resolve(__dirname, '../../dist/desktop'))

    // 并行构建所有部分
    await Promise.all([
      buildMain(),
      buildPreload(),
      // 构建渲染进程
      (async () => {
        console.log('Building renderer process...')
        await build({
          ...config.renderer,
          mode: 'production'
        })
        console.log('Renderer process built successfully!')
      })()
    ])

    // 复制资源文件
    await copyResources()

    // 复制package.json
    await copyPackageJson()

    console.log('Desktop build completed successfully!')

  } catch (error) {
    console.error('Build failed:', error)
    process.exit(1)
  }
}

// 单独构建渲染进程的函数
export async function buildRenderer() {
  console.log('Building renderer process...')

  await build({
    ...config.renderer,
    mode: 'production'
  })

  console.log('Renderer process built successfully!')
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export { main as buildDesktop }