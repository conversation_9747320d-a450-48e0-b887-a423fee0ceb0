/**
 * Electron主进程入口文件
 */

import { app, BrowserWindow, shell, ipcMain, dialog } from 'electron'
import { join } from 'path'

// 简化的工具函数
const is = {
  dev: process.env.NODE_ENV === 'development'
}

// 主窗口实例
let mainWindow: BrowserWindow | null = null

/**
 * 创建主窗口
 */
function createWindow(): void {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    minWidth: 800,
    minHeight: 600,
    title: 'Team AI 2.0',
    show: false,
    autoHideMenuBar: true,
    titleBarStyle: 'hidden',
    titleBarOverlay: {
      color: '#ffffff',
      symbolColor: '#000000',
      height: 32
    },
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      sandbox: false,
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: true
    }
  })

  // 窗口准备好后显示
  mainWindow.on('ready-to-show', () => {
    if (mainWindow) {
      mainWindow.show()
      
      // 开发环境下打开开发者工具
      // if (is.dev) {
      //   mainWindow.webContents.openDevTools()
      // }
    }
  })

  // 处理窗口关闭
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // 处理外部链接
  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // 加载应用
  if (is.dev) {
    mainWindow.loadURL('http://localhost:5173')
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

/**
 * 设置IPC处理程序
 */
function setupIpcHandlers(): void {
  // 窗口控制
  ipcMain.handle('window:minimize', () => {
    if (mainWindow) {
      mainWindow.minimize()
    }
  })

  ipcMain.handle('window:maximize', () => {
    if (mainWindow) {
      if (mainWindow.isMaximized()) {
        mainWindow.unmaximize()
      } else {
        mainWindow.maximize()
      }
    }
  })

  ipcMain.handle('window:close', () => {
    if (mainWindow) {
      mainWindow.close()
    }
  })

  ipcMain.handle('window:hide', () => {
    if (mainWindow) {
      mainWindow.hide()
    }
  })

  ipcMain.handle('window:show', () => {
    if (mainWindow) {
      mainWindow.show()
    }
  })

  ipcMain.handle('window:is-focused', () => {
    return mainWindow ? mainWindow.isFocused() : false
  })

  // 应用信息
  ipcMain.handle('app:get-version', () => {
    return app.getVersion()
  })

  ipcMain.handle('app:get-name', () => {
    return app.getName()
  })

  ipcMain.handle('app:get-path', (_, name: string) => {
    return app.getPath(name as any)
  })

  ipcMain.handle('app:quit', () => {
    app.quit()
  })

  ipcMain.handle('app:relaunch', () => {
    app.relaunch()
    app.exit()
  })

  // 对话框
  ipcMain.handle('dialog:show-message-box', async (_, options) => {
    if (mainWindow) {
      return await dialog.showMessageBox(mainWindow, options)
    }
    return await dialog.showMessageBox(options)
  })

  ipcMain.handle('dialog:show-open-dialog', async (_, options) => {
    if (mainWindow) {
      return await dialog.showOpenDialog(mainWindow, options)
    }
    return await dialog.showOpenDialog(options)
  })

  ipcMain.handle('dialog:show-save-dialog', async (_, options) => {
    if (mainWindow) {
      return await dialog.showSaveDialog(mainWindow, options)
    }
    return await dialog.showSaveDialog(options)
  })

  // Shell操作
  ipcMain.handle('shell:open-path', async (_, path: string) => {
    return await shell.openPath(path)
  })

  ipcMain.handle('shell:show-item-in-folder', (_, path: string) => {
    shell.showItemInFolder(path)
  })

  ipcMain.handle('shell:open-external', async (_, url: string) => {
    return await shell.openExternal(url)
  })
}

/**
 * 应用程序初始化
 */
function initializeApp(): void {
  // 设置应用程序用户模型ID（Windows）
  if (process.platform === 'win32') {
    app.setAppUserModelId('com.teamai.app')
  }

  // 默认在生产环境中打开或关闭DevTools
  // 使用F12切换DevTools
  app.on('browser-window-created', (_, window) => {
    // 在开发环境中启用F12快捷键
    if (is.dev) {
      window.webContents.on('before-input-event', (_, input) => {
        if (input.key === 'F12') {
          window.webContents.toggleDevTools()
        }
      })
    }
  })

  // 设置IPC处理程序
  setupIpcHandlers()

  // 应用准备就绪时创建窗口
  app.whenReady().then(() => {
    createWindow()

    app.on('activate', function () {
      // 在macOS上，当点击dock图标并且没有其他窗口打开时，
      // 通常会重新创建一个窗口
      if (BrowserWindow.getAllWindows().length === 0) {
        createWindow()
      }
    })
  })

  // 当所有窗口都关闭时退出应用程序，除了在macOS上
  // 在macOS上，应用程序及其菜单栏通常保持活动状态，
  // 直到用户使用Cmd + Q明确退出
  app.on('window-all-closed', () => {
    if (process.platform !== 'darwin') {
      app.quit()
    }
  })

  // 在这个文件中，你可以包含应用程序特定的主进程代码
  // 你也可以将它们放在单独的文件中并在这里引入
}

// 初始化应用程序
initializeApp()
