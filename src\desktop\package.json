{"name": "@team-ai/desktop", "version": "1.0.0", "description": "Team AI 2.0 Desktop Application", "main": "../../dist/desktop/main/index.js", "homepage": "./", "scripts": {"dev": "pnpm build:main && pnpm build:preload && concurrently \"pnpm dev:renderer\" \"wait-on http://localhost:5173 && cross-env NODE_ENV=development electron .\"", "dev:main": "tsx watch main/index.ts", "dev:renderer": "vite", "build": "pnpm build:main && pnpm build:renderer", "build:main": "tsc main/index.ts --outDir ../../dist/desktop/main --target es2020 --module commonjs --esModuleInterop --allowSyntheticDefaultImports --skipLibCheck", "build:preload": "tsc preload/index.ts --outDir ../../dist/desktop/preload --target es2020 --module commonjs --esModuleInterop --allowSyntheticDefaultImports --skipLibCheck", "build:renderer": "vite build --config vite.config.ts", "preview": "vite preview", "dist": "pnpm build && electron-builder", "dist:dir": "pnpm build && electron-builder --dir", "typecheck:node": "tsc --noEmit -p tsconfig.node.json", "typecheck:web": "vue-tsc --noEmit -p tsconfig.web.json", "typecheck": "pnpm run typecheck:node && pnpm run typecheck:web", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "format": "prettier --write .", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@electron-toolkit/preload": "^3.0.0", "@electron-toolkit/utils": "^3.0.0", "ant-design-vue": "^4.2.6", "common": "workspace:*", "core": "workspace:*", "electron-updater": "^6.1.7", "pinia": "^2.3.1", "vue": "^3.4.30", "vue-router": "^4.5.0"}, "devDependencies": {"@electron-toolkit/eslint-config": "^1.0.2", "@electron-toolkit/eslint-config-ts": "^2.0.0", "@electron-toolkit/tsconfig": "^1.0.1", "@rushstack/eslint-patch": "^1.10.3", "@vitejs/plugin-vue": "^5.0.5", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "cross-env": "^10.0.0", "electron": "^28.3.0", "electron-builder": "^26.0.11", "electron-vite": "^2.3.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.26.0", "prettier": "^3.3.2", "terser": "^5.44.0", "tsx": "^4.17.0", "typescript": "^5.5.2", "vite": "^5.3.1", "vue-tsc": "^2.0.22", "wait-on": "^8.0.1"}}