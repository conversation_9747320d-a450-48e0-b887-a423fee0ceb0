/**
 * Electron预加载脚本
 */

import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'

// 安全地暴露Electron API
const api = {
  // 窗口控制
  window: {
    minimize: () => ipcRenderer.invoke('window:minimize'),
    maximize: () => ipcRenderer.invoke('window:maximize'),
    unmaximize: () => ipcRenderer.invoke('window:unmaximize'),
    restore: () => ipcRenderer.invoke('window:restore'),
    close: () => ipcRenderer.invoke('window:close'),
    hide: () => ipcRenderer.invoke('window:hide'),
    show: () => ipcRenderer.invoke('window:show'),
    isFocused: () => ipcRenderer.invoke('window:is-focused')
  },

  // 应用信息
  app: {
    getVersion: () => ipcRenderer.invoke('app:get-version'),
    getName: () => ipc<PERSON><PERSON><PERSON>.invoke('app:get-name'),
    getPath: (name: string) => ipc<PERSON><PERSON><PERSON>.invoke('app:get-path', name),
    quit: () => ipc<PERSON><PERSON><PERSON>.invoke('app:quit'),
    relaunch: () => ipcRenderer.invoke('app:relaunch')
  },

  // 对话框
  dialog: {
    showMessageBox: (options: any) => ipcRenderer.invoke('dialog:show-message-box', options),
    showOpenDialog: (options: any) => ipcRenderer.invoke('dialog:show-open-dialog', options),
    showSaveDialog: (options: any) => ipcRenderer.invoke('dialog:show-save-dialog', options)
  },

  // Shell操作
  shell: {
    openPath: (path: string) => ipcRenderer.invoke('shell:open-path', path),
    showItemInFolder: (path: string) => ipcRenderer.invoke('shell:show-item-in-folder', path),
    openExternal: (url: string) => ipcRenderer.invoke('shell:open-external', url)
  },

  // 电源管理
  power: {
    getBatteryInfo: () => ipcRenderer.invoke('power:get-battery-info')
  },

  // 事件监听
  on: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.on(channel, (_event, ...args) => callback(...args))
  },

  // 事件移除
  off: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.removeListener(channel, callback as any)
  },

  // 一次性事件监听
  once: (channel: string, callback: (...args: any[]) => void) => {
    ipcRenderer.once(channel, (_event, ...args) => callback(...args))
  },

  // 发送消息到主进程
  send: (channel: string, ...args: any[]) => {
    ipcRenderer.send(channel, ...args)
  },

  // 调用主进程方法
  invoke: (channel: string, ...args: any[]) => {
    return ipcRenderer.invoke(channel, ...args)
  }
}

// 将API暴露给渲染进程
contextBridge.exposeInMainWorld('electronAPI', api)

// 也可以暴露一些实用工具
contextBridge.exposeInMainWorld('appUtils', {
  isDesktop: true,
  isWeb: false,
  platform: process.platform,
  version: process.version,
  getEnvironment: () => 'desktop'
})

// 类型声明
declare global {
  interface Window {
    electronAPI: typeof api
    appUtils: {
      isDesktop: boolean
      isWeb: boolean
      platform: string
      version: string
      getEnvironment: () => 'desktop'
    }
  }
}

// 导出类型（用于TypeScript）
export type ElectronAPI = typeof api