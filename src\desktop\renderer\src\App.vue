<template>
  <div class="app-container" :class="{ 'is-desktop': isDesktop }">
    <!-- 自定义标题栏 -->
    <div class="custom-titlebar" v-if="showTitlebar">
      <div class="titlebar-left">
        <div class="app-logo">
          <div class="logo-placeholder">AI</div>
        </div>
        <div class="app-title">Team AI 2.0</div>
      </div>
      <div class="titlebar-center">
        <div class="search-box">
          <a-input-search
            v-model:value="searchText"
            placeholder="搜索..."
            style="width: 200px"
            @search="handleSearch"
          />
        </div>
      </div>
      <div class="titlebar-right">
        <div class="titlebar-controls">
          <div class="control-button minimize" @click="minimizeWindow">
            <span class="control-icon">─</span>
          </div>
          <div class="control-button maximize" @click="maximizeWindow">
            <span class="control-icon">□</span>
          </div>
          <div class="control-button close" @click="closeWindow">
            <span class="control-icon">×</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="app-content">
      <RouterView />
    </div>

    <!-- 状态栏 -->
    <div class="status-bar" v-if="showStatusBar">
      <div class="status-left">
        <span>就绪</span>
      </div>
      <div class="status-right">
        <span>{{ currentTime }}</span>
        <span v-if="batteryInfo">{{ batteryInfo.level }}%</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'

// 路由
const route = useRoute()
const router = useRouter()

// 状态
const isDesktop = ref(true)
const searchText = ref('')
const currentTime = ref('')
const batteryInfo = ref<any>(null)

// 计算属性
const showTitlebar = computed(() => route.name !== 'fullscreen')
const showStatusBar = computed(() => route.name !== 'fullscreen')

// 方法
const minimizeWindow = () => {
  if (window.electronAPI) {
    window.electronAPI.window.minimize()
  }
}

const maximizeWindow = () => {
  if (window.electronAPI) {
    window.electronAPI.window.maximize()
  }
}

const closeWindow = () => {
  if (window.electronAPI) {
    window.electronAPI.window.close()
  }
}

const handleSearch = () => {
  if (searchText.value) {
    message.info(`搜索: ${searchText.value}`)
    searchText.value = ''
  }
}

const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString()
}

const updateBatteryInfo = async () => {
  try {
    if (window.electronAPI) {
      batteryInfo.value = await window.electronAPI.power.getBatteryInfo()
    }
  } catch (error) {
    console.error('获取电池信息失败:', error)
  }
}

// 生命周期
let timeInterval: NodeJS.Timeout
let batteryInterval: NodeJS.Timeout

onMounted(() => {
  // 更新时间
  updateTime()
  timeInterval = setInterval(updateTime, 1000)

  // 更新电池信息
  updateBatteryInfo()
  batteryInterval = setInterval(updateBatteryInfo, 30000) // 每30秒更新一次

  // 监听键盘快捷键
  const handleKeyDown = (event: KeyboardEvent) => {
    // Ctrl+W 或 Cmd+W 关闭窗口
    if ((event.ctrlKey || event.metaKey) && event.key === 'w') {
      event.preventDefault()
      closeWindow()
    }

    // Ctrl+M 或 Cmd+M 最小化窗口
    if ((event.ctrlKey || event.metaKey) && event.key === 'm') {
      event.preventDefault()
      minimizeWindow()
    }
  }

  window.addEventListener('keydown', handleKeyDown)
})

onUnmounted(() => {
  // 清理定时器
  if (timeInterval) {
    clearInterval(timeInterval)
  }
  if (batteryInterval) {
    clearInterval(batteryInterval)
  }

  // 移除事件监听
  window.removeEventListener('keydown', handleKeyDown)
})
</script>

<style lang="less">
.app-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
  overflow: hidden;

  &.is-desktop {
    // Desktop端特有样式
    .custom-titlebar {
      -webkit-app-region: drag;
      background: #fff;
      border-bottom: 1px solid #f0f0f0;
      height: 32px;
      display: flex;
      align-items: center;
      padding: 0 8px;

      .titlebar-left {
        display: flex;
        align-items: center;
        gap: 8px;

        .app-logo {
          width: 16px;
          height: 16px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .app-title {
          font-size: 12px;
          font-weight: 500;
          color: #333;
        }
      }

      .titlebar-center {
        flex: 1;
        display: flex;
        justify-content: center;
      }

      .titlebar-right {
        .titlebar-controls {
          display: flex;
          gap: 0;

          .control-button {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            -webkit-app-region: no-drag;
            transition: background-color 0.2s;

            &:hover {
              background: rgba(0, 0, 0, 0.1);
            }

            &.minimize:hover {
              background: rgba(255, 193, 7, 0.2);
            }

            &.maximize:hover {
              background: rgba(76, 175, 80, 0.2);
            }

            &.close:hover {
              background: rgba(244, 67, 54, 0.2);
            }

            .control-icon {
              font-size: 12px;
              font-weight: bold;
              color: #666;

              .close & {
                color: #666;
              }
            }
          }
        }
      }
    }

    .app-content {
      flex: 1;
      overflow: auto;
    }

    .status-bar {
      height: 24px;
      background: #f0f0f0;
      border-top: 1px solid #d9d9d9;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px;
      font-size: 12px;
      color: #666;

      .status-left,
      .status-right {
        display: flex;
        align-items: center;
        gap: 16px;
      }
    }
  }
}

// 深色主题
[data-theme="dark"] {
  .app-container.is-desktop {
    .custom-titlebar {
      background: #1f1f1f;
      border-bottom-color: #303030;

      .titlebar-left {
        .app-title {
          color: #fff;
        }
      }

      .titlebar-right {
        .control-button {
          &:hover {
            background: rgba(255, 255, 255, 0.1);
          }

          .control-icon {
            color: #fff;
          }
        }
      }
    }

    .status-bar {
      background: #303030;
      border-top-color: #404040;
      color: #bfbfbf;
    }
  }
}
</style>