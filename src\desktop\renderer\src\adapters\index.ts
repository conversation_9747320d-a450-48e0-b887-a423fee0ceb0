/**
 * Desktop端适配器实现
 */

import { IStorageAdapter, IWindowAdapter, INotificationAdapter, IFileAdapter, ISystemAdapter, INetworkAdapter, IClipboardAdapter, IThemeAdapter, ILoggerAdapter } from '@core/interfaces'

// Desktop存储适配器
export class DesktopStorageAdapter implements IStorageAdapter {
  async get(key: string): Promise<any> {
    if (window.electronAPI) {
      try {
        return await window.electronAPI.invoke('storage:get', key)
      } catch (error) {
        console.error('获取存储数据失败:', error)
        return null
      }
    }
    // 降级到 localStorage
    try {
      const value = localStorage.getItem(key)
      return value ? JSON.parse(value) : null
    } catch (error) {
      console.error('解析存储数据失败:', error)
      return null
    }
  }

  async set(key: string, value: any): Promise<void> {
    if (window.electronAPI) {
      try {
        await window.electronAPI.invoke('storage:set', key, value)
        return
      } catch (error) {
        console.error('设置存储数据失败:', error)
      }
    }
    // 降级到 localStorage
    try {
      localStorage.setItem(key, JSON.stringify(value))
    } catch (error) {
      console.error('设置本地存储失败:', error)
      throw error
    }
  }

  async remove(key: string): Promise<void> {
    if (window.electronAPI) {
      try {
        await window.electronAPI.invoke('storage:remove', key)
        return
      } catch (error) {
        console.error('删除存储数据失败:', error)
      }
    }
    // 降级到 localStorage
    localStorage.removeItem(key)
  }

  async clear(): Promise<void> {
    if (window.electronAPI) {
      try {
        await window.electronAPI.invoke('storage:clear')
        return
      } catch (error) {
        console.error('清空存储数据失败:', error)
      }
    }
    // 降级到 localStorage
    localStorage.clear()
  }

  async getAll(): Promise<Record<string, any>> {
    if (window.electronAPI) {
      try {
        return await window.electronAPI.invoke('storage:getAll')
      } catch (error) {
        console.error('获取所有存储数据失败:', error)
        return {}
      }
    }
    // 降级到 localStorage
    const result: Record<string, any> = {}
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key) {
        try {
          result[key] = JSON.parse(localStorage.getItem(key) || 'null')
        } catch (error) {
          result[key] = localStorage.getItem(key)
        }
      }
    }
    return result
  }
}

// Desktop窗口适配器
export class DesktopWindowAdapter implements IWindowAdapter {

  async minimize(): Promise<void> {
    if (window.electronAPI) {
      await window.electronAPI.window.minimize()
    }
  }

  async maximize(): Promise<void> {
    if (window.electronAPI) {
      await window.electronAPI.window.maximize()
    }
  }

  async restore(): Promise<void> {
    if (window.electronAPI) {
      await window.electronAPI.window.restore()
    }
  }

  async close(): Promise<void> {
    if (window.electronAPI) {
      await window.electronAPI.window.close()
    }
  }

  async isFullscreen(): Promise<boolean> {
    // Desktop端通常不需要全屏检测
    return false
  }

  async setFullscreen(fullscreen: boolean): Promise<void> {
    // Desktop端通常不需要全屏设置
  }

  async setTitle(title: string): Promise<void> {
    document.title = title
  }

  async focus(): Promise<void> {
    window.focus()
  }

  async blur(): Promise<void> {
    window.blur()
  }

  async isFocused(): Promise<boolean> {
    if (window.electronAPI) {
      return await window.electronAPI.window.isFocused()
    }
    return document.hasFocus()
  }
}

// Desktop通知适配器
export class DesktopNotificationAdapter implements INotificationAdapter {
  async show(options: any): Promise<void> {
    // Desktop端使用系统通知
    if ('Notification' in window) {
      const notification = new Notification(options.title, {
        body: options.body,
        icon: options.icon,
        silent: options.silent
      })

      if (options.onClick) {
        notification.onclick = options.onClick
      }
    } else {
      console.warn('浏览器不支持通知功能')
    }
  }

  async requestPermission(): Promise<string> {
    if ('Notification' in window) {
      return await Notification.requestPermission()
    }
    return 'denied'
  }

  async getPermission(): Promise<string> {
    if ('Notification' in window) {
      return Notification.permission
    }
    return 'denied'
  }

  async close(id: string): Promise<void> {
    // Desktop端无法关闭系统通知
  }

  async closeAll(): Promise<void> {
    // Desktop端无法关闭系统通知
  }
}

// Desktop文件适配器
export class DesktopFileAdapter implements IFileAdapter {

  async read(path: string): Promise<string> {
    if (window.electronAPI) {
      try {
        const result = await window.electronAPI.invoke('file:read', path)
        return result
      } catch (error) {
        throw new Error(`读取文件失败: ${error}`)
      }
    }
    throw new Error('Desktop端需要Electron API支持')
  }

  async write(path: string, content: string): Promise<void> {
    if (window.electronAPI) {
      try {
        await window.electronAPI.invoke('file:write', path, content)
      } catch (error) {
        throw new Error(`写入文件失败: ${error}`)
      }
    }
    throw new Error('Desktop端需要Electron API支持')
  }

  async exists(path: string): Promise<boolean> {
    if (window.electronAPI) {
      try {
        return await window.electronAPI.invoke('file:exists', path)
      } catch (error) {
        return false
      }
    }
    return false
  }

  async delete(path: string): Promise<void> {
    if (window.electronAPI) {
      try {
        await window.electronAPI.invoke('file:delete', path)
      } catch (error) {
        throw new Error(`删除文件失败: ${error}`)
      }
    }
    throw new Error('Desktop端需要Electron API支持')
  }

  async list(dir: string): Promise<string[]> {
    if (window.electronAPI) {
      try {
        return await window.electronAPI.invoke('file:list', dir)
      } catch (error) {
        throw new Error(`列出文件失败: ${error}`)
      }
    }
    throw new Error('Desktop端需要Electron API支持')
  }

  async openFile(options: any): Promise<string | undefined> {
    if (window.electronAPI) {
      try {
        const result = await window.electronAPI.dialog.showOpenDialog(options)
        if (!result.canceled && result.filePaths.length > 0) {
          return result.filePaths[0]
        }
      } catch (error) {
        console.error('打开文件失败:', error)
      }
    }
    return undefined
  }

  async saveFile(options: any): Promise<string | undefined> {
    if (window.electronAPI) {
      try {
        const result = await window.electronAPI.dialog.showSaveDialog(options)
        if (!result.canceled) {
          return result.filePath
        }
      } catch (error) {
        console.error('保存文件失败:', error)
      }
    }
    return undefined
  }
}

// Desktop系统适配器
export class DesktopSystemAdapter implements ISystemAdapter {

  async getPlatform(): Promise<string> {
    if (window.appUtils) {
      return window.appUtils.platform
    }
    return navigator.platform
  }

  async getEnvironment(): Promise<string> {
    if (window.appUtils) {
      return window.appUtils.getEnvironment()
    }
    return 'web'
  }

  async getVersion(): Promise<string> {
    if (window.appUtils) {
      return window.appUtils.version
    }
    return navigator.userAgent
  }

  async getAppVersion(): Promise<string> {
    if (window.electronAPI) {
      return await window.electronAPI.app.getVersion()
    }
    return '1.0.0'
  }

  async getAppName(): Promise<string> {
    if (window.electronAPI) {
      return await window.electronAPI.app.getName()
    }
    return 'Team AI 2.0'
  }

  async getAppPath(name: string): Promise<string> {
    if (window.electronAPI) {
      return await window.electronAPI.app.getPath(name)
    }
    return ''
  }

  async getBatteryInfo(): Promise<any> {
    if (window.electronAPI) {
      return await window.electronAPI.power.getBatteryInfo()
    }
    return null
  }

  async isOnline(): Promise<boolean> {
    return navigator.onLine
  }

  async getNetworkInfo(): Promise<any> {
    // 简化的网络信息
    return {
      online: navigator.onLine,
      connection: (navigator as any).connection?.effectiveType || 'unknown'
    }
  }
}

// Desktop网络适配器
export class DesktopNetworkAdapter implements INetworkAdapter {
  async get(url: string, options?: any): Promise<any> {
    const response = await fetch(url, { method: 'GET', ...options })
    return await response.json()
  }

  async post(url: string, data: any, options?: any): Promise<any> {
    const response = await fetch(url, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json', ...options?.headers },
      body: JSON.stringify(data),
      ...options
    })
    return await response.json()
  }

  async put(url: string, data: any, options?: any): Promise<any> {
    const response = await fetch(url, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json', ...options?.headers },
      body: JSON.stringify(data),
      ...options
    })
    return await response.json()
  }

  async delete(url: string, options?: any): Promise<any> {
    const response = await fetch(url, { method: 'DELETE', ...options })
    return await response.json()
  }

  async upload(url: string, file: File, options?: any): Promise<any> {
    const formData = new FormData()
    formData.append('file', file)
    const response = await fetch(url, {
      method: 'POST',
      body: formData,
      ...options
    })
    return await response.json()
  }

  async download(url: string, options?: any): Promise<Blob> {
    const response = await fetch(url, options)
    return await response.blob()
  }

  async request(config: any): Promise<any> {
    const response = await fetch(config.url, config)
    return await response.json()
  }

  async getIPAddress(): Promise<string> {
    try {
      const response = await fetch('https://api.ipify.org?format=json')
      const data = await response.json()
      return data.ip
    } catch (error) {
      console.error('获取IP地址失败:', error)
      return 'unknown'
    }
  }

  async ping(host: string): Promise<number> {
    // Desktop端无法直接ping，返回模拟值
    return Math.random() * 100 + 10
  }
}

// Desktop剪贴板适配器
export class DesktopClipboardAdapter implements IClipboardAdapter {

  async readText(): Promise<string> {
    if (navigator.clipboard) {
      return await navigator.clipboard.readText()
    }
    return ''
  }

  async writeText(text: string): Promise<void> {
    if (navigator.clipboard) {
      await navigator.clipboard.writeText(text)
    }
  }

  async readHTML(): Promise<string> {
    if (navigator.clipboard && (navigator.clipboard as any).readHTML) {
      return await (navigator.clipboard as any).readHTML()
    }
    return ''
  }

  async writeHTML(html: string): Promise<void> {
    if (navigator.clipboard && (navigator.clipboard as any).writeHTML) {
      await (navigator.clipboard as any).writeHTML(html)
    }
  }

  async readImage(): Promise<string> {
    if (navigator.clipboard && (navigator.clipboard as any).readImage) {
      return await (navigator.clipboard as any).readImage()
    }
    return ''
  }

  async writeImage(dataUrl: string): Promise<void> {
    if (navigator.clipboard && (navigator.clipboard as any).writeImage) {
      await (navigator.clipboard as any).writeImage(dataUrl)
    }
  }

  async clear(): Promise<void> {
    if (navigator.clipboard && (navigator.clipboard as any).clear) {
      await (navigator.clipboard as any).clear()
    }
  }
}

// Desktop主题适配器
export class DesktopThemeAdapter implements IThemeAdapter {

  async getCurrentTheme(): Promise<string> {
    return this.themeService.getCurrentTheme()
  }

  async setTheme(theme: string): Promise<void> {
    await this.themeService.setTheme(theme)
  }

  async getAvailableThemes(): Promise<string[]> {
    return this.themeService.getAvailableThemes()
  }

  async toggleTheme(): Promise<void> {
    await this.themeService.toggleTheme()
  }

  async isDarkMode(): Promise<boolean> {
    return this.themeService.isDarkMode()
  }

  async setDarkMode(enabled: boolean): Promise<void> {
    await this.themeService.setDarkMode(enabled)
  }

  async getSystemTheme(): Promise<string> {
    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
  }

  async listenSystemTheme(callback: (theme: string) => void): Promise<void> {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    mediaQuery.addEventListener('change', (e) => {
      callback(e.matches ? 'dark' : 'light')
    })
  }
}

// Desktop日志适配器
export class DesktopLoggerAdapter implements ILoggerAdapter {

  debug(message: string, ...args: any[]): void {
    this.loggerService.debug(message, ...args)
  }

  info(message: string, ...args: any[]): void {
    this.loggerService.info(message, ...args)
  }

  warn(message: string, ...args: any[]): void {
    this.loggerService.warn(message, ...args)
  }

  error(message: string, ...args: any[]): void {
    this.loggerService.error(message, ...args)
  }

  async log(level: string, message: string, ...args: any[]): Promise<void> {
    await this.loggerService.log(level, message, ...args)
  }

  async getLogs(options?: any): Promise<any[]> {
    return this.loggerService.getLogs(options)
  }

  async clearLogs(): Promise<void> {
    await this.loggerService.clearLogs()
  }

  async exportLogs(): Promise<string> {
    return this.loggerService.exportLogs()
  }

  async setLevel(level: string): Promise<void> {
    await this.loggerService.setLevel(level)
  }

  async getLevel(): Promise<string> {
    return this.loggerService.getLevel()
  }
}

// 设置Desktop端适配器
export function setupDesktopAdapters() {
  const adapterFactory = (window as any).AdapterFactory

  if (adapterFactory) {
    adapterFactory.setAdapter('storage', new DesktopStorageAdapter())
    adapterFactory.setAdapter('window', new DesktopWindowAdapter())
    adapterFactory.setAdapter('notification', new DesktopNotificationAdapter())
    adapterFactory.setAdapter('file', new DesktopFileAdapter())
    adapterFactory.setAdapter('system', new DesktopSystemAdapter())
    adapterFactory.setAdapter('network', new DesktopNetworkAdapter())
    adapterFactory.setAdapter('clipboard', new DesktopClipboardAdapter())
    adapterFactory.setAdapter('theme', new DesktopThemeAdapter())
    adapterFactory.setAdapter('logger', new DesktopLoggerAdapter())

    console.log('Desktop端适配器设置完成')
  }
}