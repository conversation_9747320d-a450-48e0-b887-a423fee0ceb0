/**
 * 组件样式
 */

// 按钮组件
.btn {
  display: inline-block;
  padding: 8px 16px;
  margin: 0;
  border: 1px solid transparent;
  border-radius: @border-radius-base;
  font-size: @font-size-base;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
  
  &:hover {
    opacity: 0.8;
  }
  
  &:active {
    transform: translateY(1px);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  // 按钮类型
  &.btn-primary {
    background-color: @primary-color;
    border-color: @primary-color;
    color: #fff;
  }
  
  &.btn-success {
    background-color: @success-color;
    border-color: @success-color;
    color: #fff;
  }
  
  &.btn-warning {
    background-color: @warning-color;
    border-color: @warning-color;
    color: #fff;
  }
  
  &.btn-error {
    background-color: @error-color;
    border-color: @error-color;
    color: #fff;
  }
  
  &.btn-default {
    background-color: #fff;
    border-color: @border-color-base;
    color: @text-color;
  }
  
  // 按钮大小
  &.btn-large {
    padding: 12px 24px;
    font-size: 16px;
  }
  
  &.btn-small {
    padding: 4px 8px;
    font-size: 12px;
  }
}

// 卡片组件
.card {
  background: #fff;
  border-radius: @border-radius-base;
  box-shadow: @box-shadow-base;
  overflow: hidden;
  
  .card-header {
    padding: 16px;
    border-bottom: 1px solid @border-color-base;
    
    .card-title {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: @heading-color;
    }
  }
  
  .card-body {
    padding: 16px;
  }
  
  .card-footer {
    padding: 16px;
    border-top: 1px solid @border-color-base;
    background-color: @background-color-light;
  }
}

// 输入框组件
.input {
  display: block;
  width: 100%;
  padding: 8px 12px;
  font-size: @font-size-base;
  line-height: 1.5;
  color: @text-color;
  background-color: #fff;
  border: 1px solid @border-color-base;
  border-radius: @border-radius-base;
  transition: border-color 0.3s ease;
  
  &:focus {
    border-color: @primary-color;
    box-shadow: 0 0 0 2px fade(@primary-color, 20%);
  }
  
  &:disabled {
    background-color: @background-color;
    color: @text-color-disabled;
    cursor: not-allowed;
  }
  
  &.input-error {
    border-color: @error-color;
    
    &:focus {
      border-color: @error-color;
      box-shadow: 0 0 0 2px fade(@error-color, 20%);
    }
  }
}

// 标签组件
.tag {
  display: inline-block;
  padding: 2px 8px;
  font-size: 12px;
  line-height: 1.5;
  color: @text-color;
  background-color: @background-color;
  border: 1px solid @border-color-base;
  border-radius: @border-radius-base;
  
  &.tag-primary {
    background-color: fade(@primary-color, 10%);
    border-color: @primary-color;
    color: @primary-color;
  }
  
  &.tag-success {
    background-color: fade(@success-color, 10%);
    border-color: @success-color;
    color: @success-color;
  }
  
  &.tag-warning {
    background-color: fade(@warning-color, 10%);
    border-color: @warning-color;
    color: @warning-color;
  }
  
  &.tag-error {
    background-color: fade(@error-color, 10%);
    border-color: @error-color;
    color: @error-color;
  }
}

// 徽章组件
.badge {
  display: inline-block;
  min-width: 20px;
  padding: 2px 6px;
  font-size: 12px;
  line-height: 1.5;
  text-align: center;
  color: #fff;
  background-color: @error-color;
  border-radius: 10px;
  
  &.badge-primary {
    background-color: @primary-color;
  }
  
  &.badge-success {
    background-color: @success-color;
  }
  
  &.badge-warning {
    background-color: @warning-color;
  }
  
  &.badge-error {
    background-color: @error-color;
  }
}

// 加载组件
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid fade(@primary-color, 20%);
  border-top: 2px solid @primary-color;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 分割线组件
.divider {
  margin: 16px 0;
  border-top: 1px solid @border-color-base;
  
  &.divider-vertical {
    display: inline-block;
    width: 1px;
    height: 16px;
    margin: 0 8px;
    border-top: none;
    border-left: 1px solid @border-color-base;
    vertical-align: middle;
  }
}

// 提示框组件
.tooltip {
  position: relative;
  display: inline-block;
  
  .tooltip-content {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    margin-bottom: 8px;
    padding: 8px 12px;
    font-size: 12px;
    line-height: 1.5;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.8);
    border-radius: @border-radius-base;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    
    &::after {
      content: '';
      position: absolute;
      top: 100%;
      left: 50%;
      transform: translateX(-50%);
      border: 4px solid transparent;
      border-top-color: rgba(0, 0, 0, 0.8);
    }
  }
  
  &:hover .tooltip-content {
    opacity: 1;
    visibility: visible;
  }
}

// 模态框组件
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  
  .modal-content {
    background: #fff;
    border-radius: @border-radius-base;
    box-shadow: @box-shadow-base;
    max-width: 90%;
    max-height: 90%;
    overflow: auto;
    
    .modal-header {
      padding: 16px;
      border-bottom: 1px solid @border-color-base;
      
      .modal-title {
        margin: 0;
        font-size: 16px;
        font-weight: 500;
        color: @heading-color;
      }
    }
    
    .modal-body {
      padding: 16px;
    }
    
    .modal-footer {
      padding: 16px;
      border-top: 1px solid @border-color-base;
      text-align: right;
      
      .btn + .btn {
        margin-left: 8px;
      }
    }
  }
}
