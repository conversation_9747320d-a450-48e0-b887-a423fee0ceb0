/**
 * 全局样式
 */

* {
  box-sizing: border-box;
}

html {
  font-size: @font-size-base;
  font-family: @font-family;
  line-height: @line-height-base;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  padding: 0;
  background-color: @background-color;
  color: @text-color;
  font-size: @font-size-base;
  line-height: @line-height-base;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: @background-color-light;
  border-radius: @border-radius-base;
}

::-webkit-scrollbar-thumb {
  background: @border-color-dark;
  border-radius: @border-radius-base;

  &:hover {
    background: @text-color-disabled;
  }
}

::-webkit-scrollbar-corner {
  background: @background-color-light;
}

// 链接样式
a {
  color: @primary-color;
  text-decoration: none;
  transition: @transition-fast;

  &:hover {
    color: darken(@primary-color, 10%);
    text-decoration: underline;
  }

  &:focus {
    outline: none;
  }

  &[disabled] {
    color: @text-color-disabled;
    cursor: not-allowed;
  }
}

// 按钮重置
button {
  font-family: @font-family;
  cursor: pointer;
  border: none;
  background: none;
  padding: 0;
  margin: 0;

  &:focus {
    outline: none;
  }

  &:disabled {
    cursor: not-allowed;
  }
}

// 输入框重置
input, textarea, select {
  font-family: @font-family;
  font-size: @font-size-base;
  color: @text-color;
  background-color: #fff;
  border: 1px solid @border-color;
  border-radius: @border-radius-base;
  padding: @spacing-xs @spacing-sm;
  transition: @transition-fast;

  &:focus {
    outline: none;
    border-color: @primary-color;
    box-shadow: 0 0 0 2px fade(@primary-color, 20%);
  }

  &:disabled {
    background-color: @background-color-light;
    color: @text-color-disabled;
    cursor: not-allowed;
  }

  &::placeholder {
    color: @text-color-disabled;
  }
}

// 图片样式
img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}

// 表格样式
table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
}

th, td {
  padding: @spacing-sm;
  text-align: left;
  border-bottom: 1px solid @border-color-light;
}

th {
  font-weight: 600;
  color: @heading-color;
  background-color: @background-color-light;
}

// 列表样式
ul, ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

// 代码样式
code {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  background-color: @background-color-light;
  padding: 2px 4px;
  border-radius: @border-radius-sm;
  font-size: @font-size-sm;
  color: @error-color;
}

pre {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  background-color: @background-color-light;
  padding: @spacing-md;
  border-radius: @border-radius-base;
  overflow-x: auto;
  margin: @spacing-md 0;

  code {
    background: none;
    padding: 0;
    color: @text-color;
  }
}

// 分割线
hr {
  border: none;
  border-top: 1px solid @border-color-light;
  margin: @spacing-md 0;
}

// 字体样式
h1, h2, h3, h4, h5, h6 {
  margin: 0 0 @spacing-md 0;
  color: @heading-color;
  font-weight: 600;
  line-height: 1.2;
}

h1 { font-size: 32px; }
h2 { font-size: 24px; }
h3 { font-size: 20px; }
h4 { font-size: 16px; }
h5 { font-size: 14px; }
h6 { font-size: 12px; }

p {
  margin: 0 0 @spacing-md 0;
  line-height: @line-height-base;
}

// 引用样式
blockquote {
  margin: @spacing-md 0;
  padding: @spacing-sm @spacing-md;
  border-left: 4px solid @primary-color;
  background-color: @background-color-light;
  color: @text-color-secondary;
  font-style: italic;
}

// 标记样式
mark {
  background-color: @warning-color;
  color: #fff;
  padding: 2px 4px;
  border-radius: @border-radius-sm;
}

// 缩写样式
abbr[title] {
  border-bottom: 1px dotted @text-color-secondary;
  cursor: help;
}

// 键盘样式
kbd {
  background-color: @background-color-dark;
  border: 1px solid @border-color;
  border-radius: @border-radius-sm;
  padding: 2px 4px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: @font-size-sm;
  color: @text-color;
}

// 示例样式
.sample {
  background-color: @background-color-light;
  border: 1px solid @border-color-light;
  border-radius: @border-radius-base;
  padding: @spacing-md;
  margin: @spacing-md 0;

  .sample-title {
    font-weight: 600;
    color: @heading-color;
    margin-bottom: @spacing-sm;
  }

  .sample-content {
    color: @text-color-secondary;
  }
}

// 辅助类
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

.text-primary { color: @primary-color; }
.text-success { color: @success-color; }
.text-warning { color: @warning-color; }
.text-error { color: @error-color; }
.text-info { color: @info-color; }
.text-muted { color: @text-color-secondary; }

.bg-primary { background-color: @primary-color; }
.bg-success { background-color: @success-color; }
.bg-warning { background-color: @warning-color; }
.bg-error { background-color: @error-color; }
.bg-info { background-color: @info-color; }
.bg-light { background-color: @background-color-light; }
.bg-dark { background-color: @background-color-dark; }

.border { border: 1px solid @border-color; }
.border-top { border-top: 1px solid @border-color; }
.border-right { border-right: 1px solid @border-color; }
.border-bottom { border-bottom: 1px solid @border-color; }
.border-left { border-left: 1px solid @border-color; }

.rounded { border-radius: @border-radius-base; }
.rounded-sm { border-radius: @border-radius-sm; }
.rounded-lg { border-radius: @border-radius-lg; }
.rounded-full { border-radius: @border-radius-round; }

.shadow-sm { box-shadow: @shadow-sm; }
.shadow { box-shadow: @shadow-base; }
.shadow-lg { box-shadow: @shadow-lg; }
.shadow-xl { box-shadow: @shadow-xl; }

.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }
.d-inline-flex { display: inline-flex; }

.flex-column { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.justify-content-start { justify-content: flex-start; }
.justify-content-end { justify-content: flex-end; }
.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.justify-content-around { justify-content: space-around; }

.align-items-start { align-items: flex-start; }
.align-items-end { align-items: flex-end; }
.align-items-center { align-items: center; }
.align-items-baseline { align-items: baseline; }
.align-items-stretch { align-items: stretch; }

.flex-grow-1 { flex-grow: 1; }
.flex-shrink-1 { flex-shrink: 1; }
.flex-fill { flex: 1 1 auto; }

.position-static { position: static; }
.position-relative { position: relative; }
.position-absolute { position: absolute; }
.position-fixed { position: fixed; }
.position-sticky { position: sticky; }

.w-25 { width: 25%; }
.w-50 { width: 50%; }
.w-75 { width: 75%; }
.w-100 { width: 100%; }
.w-auto { width: auto; }

.h-25 { height: 25%; }
.h-50 { height: 50%; }
.h-75 { height: 75%; }
.h-100 { height: 100%; }
.h-auto { height: auto; }

.m-0 { margin: 0; }
.mt-0 { margin-top: 0; }
.mr-0 { margin-right: 0; }
.mb-0 { margin-bottom: 0; }
.ml-0 { margin-left: 0; }
.mx-0 { margin-left: 0; margin-right: 0; }
.my-0 { margin-top: 0; margin-bottom: 0; }

.p-0 { padding: 0; }
.pt-0 { padding-top: 0; }
.pr-0 { padding-right: 0; }
.pb-0 { padding-bottom: 0; }
.pl-0 { padding-left: 0; }
.px-0 { padding-left: 0; padding-right: 0; }
.py-0 { padding-top: 0; padding-bottom: 0; }

// 间距工具类 - 简化版本
.m-xs { margin: @spacing-xs; }
.m-sm { margin: @spacing-sm; }
.m-md { margin: @spacing-md; }
.m-lg { margin: @spacing-lg; }
.m-xl { margin: @spacing-xl; }

.mt-xs { margin-top: @spacing-xs; }
.mt-sm { margin-top: @spacing-sm; }
.mt-md { margin-top: @spacing-md; }
.mt-lg { margin-top: @spacing-lg; }
.mt-xl { margin-top: @spacing-xl; }

.mr-xs { margin-right: @spacing-xs; }
.mr-sm { margin-right: @spacing-sm; }
.mr-md { margin-right: @spacing-md; }
.mr-lg { margin-right: @spacing-lg; }
.mr-xl { margin-right: @spacing-xl; }

.mb-xs { margin-bottom: @spacing-xs; }
.mb-sm { margin-bottom: @spacing-sm; }
.mb-md { margin-bottom: @spacing-md; }
.mb-lg { margin-bottom: @spacing-lg; }
.mb-xl { margin-bottom: @spacing-xl; }

.ml-xs { margin-left: @spacing-xs; }
.ml-sm { margin-left: @spacing-sm; }
.ml-md { margin-left: @spacing-md; }
.ml-lg { margin-left: @spacing-lg; }
.ml-xl { margin-left: @spacing-xl; }

.p-xs { padding: @spacing-xs; }
.p-sm { padding: @spacing-sm; }
.p-md { padding: @spacing-md; }
.p-lg { padding: @spacing-lg; }
.p-xl { padding: @spacing-xl; }

.pt-xs { padding-top: @spacing-xs; }
.pt-sm { padding-top: @spacing-sm; }
.pt-md { padding-top: @spacing-md; }
.pt-lg { padding-top: @spacing-lg; }
.pt-xl { padding-top: @spacing-xl; }

.pr-xs { padding-right: @spacing-xs; }
.pr-sm { padding-right: @spacing-sm; }
.pr-md { padding-right: @spacing-md; }
.pr-lg { padding-right: @spacing-lg; }
.pr-xl { padding-right: @spacing-xl; }

.pb-xs { padding-bottom: @spacing-xs; }
.pb-sm { padding-bottom: @spacing-sm; }
.pb-md { padding-bottom: @spacing-md; }
.pb-lg { padding-bottom: @spacing-lg; }
.pb-xl { padding-bottom: @spacing-xl; }

.pl-xs { padding-left: @spacing-xs; }
.pl-sm { padding-left: @spacing-sm; }
.pl-md { padding-left: @spacing-md; }
.pl-lg { padding-left: @spacing-lg; }
.pl-xl { padding-left: @spacing-xl; }