/**
 * Less Mixins
 */

// 清除浮动
.clearfix() {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

// 文本省略
.text-ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 多行文本省略
.text-ellipsis-multiline(@lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: @lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

// 居中对齐
.center() {
  display: flex;
  align-items: center;
  justify-content: center;
}

// 垂直居中
.center-vertical() {
  display: flex;
  align-items: center;
}

// 水平居中
.center-horizontal() {
  display: flex;
  justify-content: center;
}

// 绝对定位居中
.absolute-center() {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

// 圆角
.border-radius(@radius: 4px) {
  border-radius: @radius;
}

// 阴影
.box-shadow(@shadow: 0 2px 8px rgba(0, 0, 0, 0.15)) {
  box-shadow: @shadow;
}

// 过渡动画
.transition(@property: all, @duration: 0.3s, @timing: ease) {
  transition: @property @duration @timing;
}

// 渐变背景
.gradient(@start-color, @end-color, @direction: to bottom) {
  background: linear-gradient(@direction, @start-color, @end-color);
}

// 按钮样式
.button-style(@bg-color: @primary-color, @text-color: #fff, @border-color: @bg-color) {
  background-color: @bg-color;
  color: @text-color;
  border: 1px solid @border-color;
  border-radius: @border-radius-base;
  padding: 8px 16px;
  cursor: pointer;
  .transition();
  
  &:hover {
    opacity: 0.8;
  }
  
  &:active {
    transform: translateY(1px);
  }
}

// 卡片样式
.card-style(@padding: 16px, @radius: @border-radius-base) {
  background: #fff;
  border-radius: @radius;
  padding: @padding;
  .box-shadow();
}

// 响应式断点
.mobile(@rules) {
  @media (max-width: 768px) {
    @rules();
  }
}

.tablet(@rules) {
  @media (min-width: 769px) and (max-width: 1024px) {
    @rules();
  }
}

.desktop(@rules) {
  @media (min-width: 1025px) {
    @rules();
  }
}

// 滚动条样式
.scrollbar-style(@width: 8px, @track-color: #f1f1f1, @thumb-color: #c1c1c1) {
  &::-webkit-scrollbar {
    width: @width;
  }
  
  &::-webkit-scrollbar-track {
    background: @track-color;
  }
  
  &::-webkit-scrollbar-thumb {
    background: @thumb-color;
    border-radius: @width / 2;
    
    &:hover {
      background: darken(@thumb-color, 10%);
    }
  }
}

// 加载动画
.loading-spinner(@size: 20px, @color: @primary-color) {
  width: @size;
  height: @size;
  border: 2px solid fade(@color, 20%);
  border-top: 2px solid @color;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// 脉冲动画
.pulse-animation(@duration: 2s) {
  animation: pulse @duration infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.05);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

// 淡入动画
.fade-in(@duration: 0.3s) {
  animation: fadeIn @duration ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

// 滑入动画
.slide-in-up(@duration: 0.3s) {
  animation: slideInUp @duration ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}
