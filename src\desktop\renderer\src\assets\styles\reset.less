/**
 * CSS Reset
 */

/* Reset margins, paddings, and borders */
* {
  margin: 0;
  padding: 0;
  border: 0;
  box-sizing: border-box;
}

/* HTML5 display-role reset for older browsers */
article, aside, details, figcaption, figure,
footer, header, hgroup, menu, nav, section {
  display: block;
}

/* Set default font family and line height */
body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  line-height: 1.5;
  color: #333;
  background-color: #fff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Remove list styles */
ol, ul {
  list-style: none;
}

/* Remove quotes from blockquotes and q elements */
blockquote, q {
  quotes: none;
}

blockquote:before, blockquote:after,
q:before, q:after {
  content: '';
  content: none;
}

/* Collapse border model for tables */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* Remove default button styles */
button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  cursor: pointer;
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

/* Remove default input styles */
input, textarea, select {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  border: none;
  outline: none;
  background: none;
}

/* Remove default link styles */
a {
  color: inherit;
  text-decoration: none;
}

a:hover {
  text-decoration: none;
}

/* Remove default heading margins */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: normal;
}

/* Remove default paragraph margins */
p {
  margin: 0;
}

/* Remove default form element margins */
form {
  margin: 0;
}

/* Remove default fieldset styles */
fieldset {
  border: none;
  margin: 0;
  padding: 0;
}

/* Remove default legend styles */
legend {
  border: none;
  padding: 0;
}

/* Set consistent image display */
img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
  border: none;
}

/* Remove default iframe border */
iframe {
  border: none;
}

/* Set consistent hr styles */
hr {
  border: none;
  height: 1px;
  background-color: #e8e8e8;
  margin: 16px 0;
}

/* Remove default address styles */
address {
  font-style: normal;
}

/* Remove default cite styles */
cite {
  font-style: normal;
}

/* Remove default code styles */
code, kbd, pre, samp {
  font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
}

/* Remove default small styles */
small {
  font-size: inherit;
}

/* Remove default sub and sup styles */
sub, sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sup {
  top: -0.5em;
}

sub {
  bottom: -0.25em;
}

/* Remove default mark styles */
mark {
  background-color: transparent;
  color: inherit;
}

/* Remove default del styles */
del {
  text-decoration: none;
}

/* Remove default ins styles */
ins {
  text-decoration: none;
}

/* Set consistent selection styles */
::selection {
  background-color: #b3d4fc;
  text-shadow: none;
}

/* Remove default focus outline */
:focus {
  outline: none;
}

/* Remove default webkit appearance */
input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration {
  -webkit-appearance: none;
}

/* Remove default number input spinners */
input[type="number"]::-webkit-outer-spin-button,
input[type="number"]::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

/* Remove default textarea resize */
textarea {
  resize: vertical;
}

/* Set consistent placeholder styles */
::placeholder {
  color: #999;
  opacity: 1;
}

/* Remove default webkit tap highlight */
* {
  -webkit-tap-highlight-color: transparent;
}

/* Set consistent scrollbar styles for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Ensure consistent box-sizing */
*,
*::before,
*::after {
  box-sizing: border-box;
}
