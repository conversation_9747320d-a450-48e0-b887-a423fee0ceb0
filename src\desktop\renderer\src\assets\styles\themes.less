/**
 * 主题样式
 */

// 浅色主题（默认）
:root {
  // 颜色
  --primary-color: @primary-color;
  --success-color: @success-color;
  --warning-color: @warning-color;
  --error-color: @error-color;
  --info-color: @info-color;

  // 文字颜色
  --text-color: @text-color;
  --text-color-secondary: @text-color-secondary;
  --text-color-disabled: @text-color-disabled;
  --heading-color: @heading-color;

  // 背景颜色
  --background-color: @background-color;
  --background-color-light: @background-color-light;
  --background-color-dark: @background-color-dark;

  // 边框颜色
  --border-color: @border-color;
  --border-color-light: @border-color-light;
  --border-color-dark: @border-color-dark;

  // 阴影
  --shadow-sm: @shadow-sm;
  --shadow-base: @shadow-base;
  --shadow-lg: @shadow-lg;
  --shadow-xl: @shadow-xl;

  // 特有颜色
  --component-background: #fff;
  --popover-background: #fff;
  --modal-background: #fff;
  --tooltip-background: rgba(0, 0, 0, 0.75);

  // 链接颜色
  --link-color: @primary-color;
  --link-hover-color: darken(@primary-color, 10%);
  --link-active-color: darken(@primary-color, 20%);

  // 按钮颜色
  --button-primary-bg: @primary-color;
  --button-primary-border: @primary-color;
  --button-primary-hover-bg: darken(@primary-color, 10%);
  --button-primary-hover-border: darken(@primary-color, 10%);
  --button-primary-active-bg: darken(@primary-color, 20%);
  --button-primary-active-border: darken(@primary-color, 20%);

  --button-default-bg: #fff;
  --button-default-border: @border-color;
  --button-default-hover-bg: @background-color-light;
  --button-default-hover-border: @primary-color;
  --button-default-active-bg: @background-color-dark;
  --button-default-active-border: darken(@primary-color, 10%);

  --button-ghost-bg: transparent;
  --button-ghost-border: @border-color;
  --button-ghost-hover-bg: @background-color-light;
  --button-ghost-hover-border: @primary-color;
  --button-ghost-active-bg: @background-color-dark;
  --button-ghost-active-border: darken(@primary-color, 10%);

  --button-danger-bg: @error-color;
  --button-danger-border: @error-color;
  --button-danger-hover-bg: darken(@error-color, 10%);
  --button-danger-hover-border: darken(@error-color, 10%);
  --button-danger-active-bg: darken(@error-color, 20%);
  --button-danger-active-border: darken(@error-color, 20%);

  // 输入框颜色
  --input-bg: #fff;
  --input-border: @border-color;
  --input-hover-border: @primary-color;
  --input-focus-border: @primary-color;
  --input-focus-shadow: fade(@primary-color, 20%);
  --input-disabled-bg: @background-color-light;
  --input-placeholder: @text-color-disabled;

  // 选择器颜色
  --select-bg: #fff;
  --select-border: @border-color;
  --select-hover-bg: @background-color-light;
  --select-selected-bg: fade(@primary-color, 10%);

  // 表格颜色
  --table-header-bg: @background-color-light;
  --table-row-hover-bg: @background-color-light;
  --table-row-selected-bg: fade(@primary-color, 10%);
  --table-border-color: @border-color-light;

  // 模态框颜色
  --modal-mask-bg: rgba(0, 0, 0, 0.45);
  --modal-header-bg: @background-color-light;
  --modal-footer-bg: @background-color-light;

  // 标签页颜色
  --tabs-card-bg: @background-color-light;
  --tabs-active-color: @primary-color;
  --tabs-hover-color: @primary-color;
  --tabs-border-color: @border-color-light;

  // 菜单颜色
  --menu-bg: @background-color-light;
  --menu-item-bg: transparent;
  --menu-item-hover-bg: @background-color-dark;
  --menu-item-selected-bg: fade(@primary-color, 10%);
  --menu-item-color: @text-color;
  --menu-item-selected-color: @primary-color;

  // 工具提示颜色
  --tooltip-bg: rgba(0, 0, 0, 0.75);
  --tooltip-color: #fff;

  // 标题栏颜色
  --titlebar-bg: #fff;
  --titlebar-border: @border-color-light;
  --titlebar-text: @text-color;
  --titlebar-icon: @text-color-secondary;

  // 状态栏颜色
  --statusbar-bg: @background-color-light;
  --statusbar-border: @border-color-light;
  --statusbar-text: @text-color-secondary;

  // 卡片颜色
  --card-bg: #fff;
  --card-border: @border-color-light;
  --card-shadow: @shadow-base;
}

// 深色主题
[data-theme="dark"] {
  // 基础颜色
  --primary-color: #40a9ff;
  --success-color: #73d13d;
  --warning-color: #ffc53d;
  --error-color: #ff4d4f;
  --info-color: #40a9ff;

  // 文字颜色
  --text-color: #fff;
  --text-color-secondary: #bfbfbf;
  --text-color-disabled: #595959;
  --heading-color: #fff;

  // 背景颜色
  --background-color: #141414;
  --background-color-light: #1f1f1f;
  --background-color-dark: #262626;

  // 边框颜色
  --border-color: #434343;
  --border-color-light: #303030;
  --border-color-dark: #555555;

  // 阴影
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.3);
  --shadow-base: 0 4px 8px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.3);
  --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.3);

  // 特有颜色
  --component-background: #1f1f1f;
  --popover-background: #1f1f1f;
  --modal-background: #1f1f1f;
  --tooltip-background: rgba(0, 0, 0, 0.85);

  // 链接颜色
  --link-color: #40a9ff;
  --link-hover-color: #69c0ff;
  --link-active-color: #1890ff;

  // 按钮颜色
  --button-primary-bg: #177ddc;
  --button-primary-border: #177ddc;
  --button-primary-hover-bg: #40a9ff;
  --button-primary-hover-border: #40a9ff;
  --button-primary-active-bg: #096dd9;
  --button-primary-active-border: #096dd9;

  --button-default-bg: transparent;
  --button-default-border: #434343;
  --button-default-hover-bg: #303030;
  --button-default-hover-border: #40a9ff;
  --button-default-active-bg: #262626;
  --button-default-active-border: #177ddc;

  --button-ghost-bg: transparent;
  --button-ghost-border: #434343;
  --button-ghost-hover-bg: #303030;
  --button-ghost-hover-border: #40a9ff;
  --button-ghost-active-bg: #262626;
  --button-ghost-active-border: #177ddc;

  --button-danger-bg: #a61d24;
  --button-danger-border: #a61d24;
  --button-danger-hover-bg: #ff4d4f;
  --button-danger-hover-border: #ff4d4f;
  --button-danger-active-bg: #cf1322;
  --button-danger-active-border: #cf1322;

  // 输入框颜色
  --input-bg: #1f1f1f;
  --input-border: #434343;
  --input-hover-border: #40a9ff;
  --input-focus-border: #40a9ff;
  --input-focus-shadow: rgba(64, 169, 255, 0.2);
  --input-disabled-bg: #303030;
  --input-placeholder: #595959;

  // 选择器颜色
  --select-bg: #1f1f1f;
  --select-border: #434343;
  --select-hover-bg: #303030;
  --select-selected-bg: rgba(64, 169, 255, 0.2);

  // 表格颜色
  --table-header-bg: #262626;
  --table-row-hover-bg: #303030;
  --table-row-selected-bg: rgba(64, 169, 255, 0.2);
  --table-border-color: #303030;

  // 模态框颜色
  --modal-mask-bg: rgba(0, 0, 0, 0.65);
  --modal-header-bg: #262626;
  --modal-footer-bg: #262626;

  // 标签页颜色
  --tabs-card-bg: #262626;
  --tabs-active-color: #40a9ff;
  --tabs-hover-color: #40a9ff;
  --tabs-border-color: #303030;

  // 菜单颜色
  --menu-bg: #262626;
  --menu-item-bg: transparent;
  --menu-item-hover-bg: #303030;
  --menu-item-selected-bg: rgba(64, 169, 255, 0.2);
  --menu-item-color: #bfbfbf;
  --menu-item-selected-color: #40a9ff;

  // 工具提示颜色
  --tooltip-bg: rgba(0, 0, 0, 0.85);
  --tooltip-color: #fff;

  // 标题栏颜色
  --titlebar-bg: #1f1f1f;
  --titlebar-border: #303030;
  --titlebar-text: #fff;
  --titlebar-icon: #bfbfbf;

  // 状态栏颜色
  --statusbar-bg: #262626;
  --statusbar-border: #303030;
  --statusbar-text: #bfbfbf;

  // 卡片颜色
  --card-bg: #1f1f1f;
  --card-border: #303030;
  --card-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);

  // 应用深色主题到全局
  background-color: var(--background-color);
  color: var(--text-color);

  // 滚动条样式
  ::-webkit-scrollbar-track {
    background: var(--background-color-dark);
  }

  ::-webkit-scrollbar-thumb {
    background: var(--border-color-dark);

    &:hover {
      background: var(--text-color-disabled);
    }
  }

  // 链接样式
  a {
    color: var(--link-color);

    &:hover {
      color: var(--link-hover-color);
    }

    &:active {
      color: var(--link-active-color);
    }
  }

  // 按钮样式
  .ant-btn {
    background: var(--button-default-bg);
    border-color: var(--button-default-border);
    color: var(--text-color);

    &:hover {
      background: var(--button-default-hover-bg);
      border-color: var(--button-default-hover-border);
      color: var(--text-color);
    }

    &.ant-btn-primary {
      background: var(--button-primary-bg);
      border-color: var(--button-primary-border);
      color: #fff;

      &:hover {
        background: var(--button-primary-hover-bg);
        border-color: var(--button-primary-hover-border);
        color: #fff;
      }
    }

    &.ant-btn-danger {
      background: var(--button-danger-bg);
      border-color: var(--button-danger-border);
      color: #fff;

      &:hover {
        background: var(--button-danger-hover-bg);
        border-color: var(--button-danger-hover-border);
        color: #fff;
      }
    }

    &.ant-btn-ghost {
      background: var(--button-ghost-bg);
      border-color: var(--button-ghost-border);
      color: var(--text-color);

      &:hover {
        background: var(--button-ghost-hover-bg);
        border-color: var(--button-ghost-hover-border);
        color: var(--text-color);
      }
    }
  }

  // 输入框样式
  .ant-input, .ant-select-selector {
    background: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-color);

    &:hover {
      border-color: var(--input-hover-border);
    }

    &:focus {
      border-color: var(--input-focus-border);
      box-shadow: 0 0 0 2px var(--input-focus-shadow);
    }

    &::placeholder {
      color: var(--input-placeholder);
    }
  }

  // 卡片样式
  .ant-card {
    background: var(--card-bg);
    border-color: var(--card-border);
    color: var(--text-color);
  }

  // 表格样式
  .ant-table {
    background: var(--background-color);
    color: var(--text-color);

    .ant-table-thead > tr > th {
      background: var(--table-header-bg);
      border-bottom-color: var(--table-border-color);
      color: var(--heading-color);
    }

    .ant-table-tbody > tr {
      &:hover > td {
        background: var(--table-row-hover-bg);
      }

      &.ant-table-row-selected {
        background: var(--table-row-selected-bg);
      }
    }
  }

  // 模态框样式
  .ant-modal {
    .ant-modal-content {
      background: var(--modal-background);
      color: var(--text-color);
    }

    .ant-modal-header {
      background: var(--modal-header-bg);
      border-bottom-color: var(--border-color-light);
    }

    .ant-modal-footer {
      background: var(--modal-footer-bg);
      border-top-color: var(--border-color-light);
    }
  }

  // 标签页样式
  .ant-tabs {
    .ant-tabs-card-bar {
      .ant-tabs-tab {
        background: var(--tabs-card-bg);
        border-color: var(--tabs-border-color);
        color: var(--text-color);

        &:hover {
          color: var(--tabs-hover-color);
        }

        &.ant-tabs-tab-active {
          color: var(--tabs-active-color);
        }
      }
    }
  }

  // 菜单样式
  .ant-menu {
    background: var(--menu-bg);
    color: var(--menu-item-color);

    .ant-menu-item {
      &:hover {
        background: var(--menu-item-hover-bg);
        color: var(--menu-item-selected-color);
      }

      &.ant-menu-item-selected {
        background: var(--menu-item-selected-bg);
        color: var(--menu-item-selected-color);
      }
    }
  }
}

// 高对比度主题
@media (prefers-contrast: high) {
  :root {
    --border-color: #000;
    --border-color-light: #666;
    --border-color-dark: #000;
    --text-color: #000;
    --text-color-secondary: #333;
    --heading-color: #000;
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.5);
    --shadow-base: 0 4px 8px rgba(0, 0, 0, 0.5);
    --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
    --shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.5);
  }

  [data-theme="dark"] {
    --border-color: #fff;
    --border-color-light: #ccc;
    --border-color-dark: #fff;
    --text-color: #fff;
    --text-color-secondary: #ccc;
    --heading-color: #fff;
  }
}

// 减少动画主题
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}