/**
 * 工具类样式
 */

// 颜色工具类
.text-primary { color: @primary-color; }
.text-success { color: @success-color; }
.text-warning { color: @warning-color; }
.text-error { color: @error-color; }
.text-info { color: @info-color; }
.text-muted { color: @text-color-secondary; }
.text-white { color: #fff; }
.text-black { color: #000; }

.bg-primary { background-color: @primary-color; }
.bg-success { background-color: @success-color; }
.bg-warning { background-color: @warning-color; }
.bg-error { background-color: @error-color; }
.bg-info { background-color: @info-color; }
.bg-light { background-color: @background-color-light; }
.bg-dark { background-color: #333; }
.bg-white { background-color: #fff; }
.bg-transparent { background-color: transparent; }

// 边框工具类
.border { border: 1px solid @border-color-base; }
.border-top { border-top: 1px solid @border-color-base; }
.border-right { border-right: 1px solid @border-color-base; }
.border-bottom { border-bottom: 1px solid @border-color-base; }
.border-left { border-left: 1px solid @border-color-base; }
.border-0 { border: 0; }
.border-top-0 { border-top: 0; }
.border-right-0 { border-right: 0; }
.border-bottom-0 { border-bottom: 0; }
.border-left-0 { border-left: 0; }

.border-primary { border-color: @primary-color; }
.border-success { border-color: @success-color; }
.border-warning { border-color: @warning-color; }
.border-error { border-color: @error-color; }
.border-info { border-color: @info-color; }

// 圆角工具类
.rounded { border-radius: @border-radius-base; }
.rounded-sm { border-radius: 2px; }
.rounded-lg { border-radius: 8px; }
.rounded-xl { border-radius: 12px; }
.rounded-circle { border-radius: 50%; }
.rounded-0 { border-radius: 0; }

.rounded-top { border-top-left-radius: @border-radius-base; border-top-right-radius: @border-radius-base; }
.rounded-right { border-top-right-radius: @border-radius-base; border-bottom-right-radius: @border-radius-base; }
.rounded-bottom { border-bottom-left-radius: @border-radius-base; border-bottom-right-radius: @border-radius-base; }
.rounded-left { border-top-left-radius: @border-radius-base; border-bottom-left-radius: @border-radius-base; }

// 阴影工具类
.shadow { box-shadow: @box-shadow-base; }
.shadow-sm { box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); }
.shadow-lg { box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15); }
.shadow-xl { box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2); }
.shadow-none { box-shadow: none; }

// 字体工具类
.font-weight-light { font-weight: 300; }
.font-weight-normal { font-weight: 400; }
.font-weight-medium { font-weight: 500; }
.font-weight-bold { font-weight: 700; }

.font-size-xs { font-size: 12px; }
.font-size-sm { font-size: 14px; }
.font-size-base { font-size: @font-size-base; }
.font-size-lg { font-size: 18px; }
.font-size-xl { font-size: 20px; }
.font-size-xxl { font-size: 24px; }

.line-height-1 { line-height: 1; }
.line-height-sm { line-height: 1.25; }
.line-height-base { line-height: 1.5; }
.line-height-lg { line-height: 1.75; }

// 文本装饰
.text-decoration-none { text-decoration: none; }
.text-decoration-underline { text-decoration: underline; }
.text-decoration-line-through { text-decoration: line-through; }

// 文本转换
.text-lowercase { text-transform: lowercase; }
.text-uppercase { text-transform: uppercase; }
.text-capitalize { text-transform: capitalize; }

// 用户选择
.user-select-all { user-select: all; }
.user-select-auto { user-select: auto; }
.user-select-none { user-select: none; }

// 指针事件
.pointer-events-none { pointer-events: none; }
.pointer-events-auto { pointer-events: auto; }

// 光标样式
.cursor-auto { cursor: auto; }
.cursor-default { cursor: default; }
.cursor-pointer { cursor: pointer; }
.cursor-wait { cursor: wait; }
.cursor-text { cursor: text; }
.cursor-move { cursor: move; }
.cursor-not-allowed { cursor: not-allowed; }

// 透明度
.opacity-0 { opacity: 0; }
.opacity-25 { opacity: 0.25; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

// 可见性
.visible { visibility: visible; }
.invisible { visibility: hidden; }

// z-index
.z-index-0 { z-index: 0; }
.z-index-1 { z-index: 1; }
.z-index-2 { z-index: 2; }
.z-index-3 { z-index: 3; }
.z-index-auto { z-index: auto; }

// 过渡动画
.transition { transition: all 0.3s ease; }
.transition-none { transition: none; }
.transition-fast { transition: all 0.15s ease; }
.transition-slow { transition: all 0.6s ease; }

// 变换
.transform { transform: translateZ(0); }
.transform-none { transform: none; }

.scale-50 { transform: scale(0.5); }
.scale-75 { transform: scale(0.75); }
.scale-100 { transform: scale(1); }
.scale-125 { transform: scale(1.25); }
.scale-150 { transform: scale(1.5); }

.rotate-0 { transform: rotate(0deg); }
.rotate-45 { transform: rotate(45deg); }
.rotate-90 { transform: rotate(90deg); }
.rotate-180 { transform: rotate(180deg); }

// 滤镜
.filter-none { filter: none; }
.filter-blur { filter: blur(4px); }
.filter-brightness { filter: brightness(1.25); }
.filter-contrast { filter: contrast(1.25); }
.filter-grayscale { filter: grayscale(100%); }
.filter-invert { filter: invert(100%); }
.filter-sepia { filter: sepia(100%); }

// 背景
.bg-cover { background-size: cover; }
.bg-contain { background-size: contain; }
.bg-auto { background-size: auto; }
.bg-center { background-position: center; }
.bg-top { background-position: top; }
.bg-right { background-position: right; }
.bg-bottom { background-position: bottom; }
.bg-left { background-position: left; }
.bg-no-repeat { background-repeat: no-repeat; }
.bg-repeat { background-repeat: repeat; }
.bg-repeat-x { background-repeat: repeat-x; }
.bg-repeat-y { background-repeat: repeat-y; }

// 对象适应
.object-contain { object-fit: contain; }
.object-cover { object-fit: cover; }
.object-fill { object-fit: fill; }
.object-none { object-fit: none; }
.object-scale-down { object-fit: scale-down; }

// 对象位置
.object-bottom { object-position: bottom; }
.object-center { object-position: center; }
.object-left { object-position: left; }
.object-left-bottom { object-position: left bottom; }
.object-left-top { object-position: left top; }
.object-right { object-position: right; }
.object-right-bottom { object-position: right bottom; }
.object-right-top { object-position: right top; }
.object-top { object-position: top; }

// 列表样式
.list-none { list-style: none; }
.list-disc { list-style-type: disc; }
.list-decimal { list-style-type: decimal; }

// 表格
.table-auto { table-layout: auto; }
.table-fixed { table-layout: fixed; }

// 边框折叠
.border-collapse { border-collapse: collapse; }
.border-separate { border-collapse: separate; }

// 空白字符
.whitespace-normal { white-space: normal; }
.whitespace-nowrap { white-space: nowrap; }
.whitespace-pre { white-space: pre; }
.whitespace-pre-line { white-space: pre-line; }
.whitespace-pre-wrap { white-space: pre-wrap; }

// 单词换行
.break-normal { overflow-wrap: normal; word-break: normal; }
.break-words { overflow-wrap: break-word; }
.break-all { word-break: break-all; }

// 截断
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 屏幕阅读器
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.not-sr-only {
  position: static;
  width: auto;
  height: auto;
  padding: 0;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
}
