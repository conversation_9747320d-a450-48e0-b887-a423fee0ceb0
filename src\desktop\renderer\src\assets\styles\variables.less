/**
 * 样式变量定义
 */

// 颜色
@primary-color: #1890ff;
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #f5222d;
@info-color: #1890ff;

// 文字颜色
@text-color: #333;
@text-color-secondary: #666;
@text-color-disabled: #999;
@heading-color: #333;

// 背景颜色
@background-color: #f5f5f5;
@background-color-light: #fafafa;
@background-color-dark: #f0f0f0;

// 边框颜色
@border-color: #d9d9d9;
@border-color-base: #d9d9d9;
@border-color-light: #f0f0f0;
@border-color-dark: #bfbfbf;

// 字体
@font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
@font-size-base: 14px;
@font-size-lg: 16px;
@font-size-sm: 12px;
@line-height-base: 1.5;

// 间距
@spacing-unit: 8px;
@spacing-xs: @spacing-unit;        // 8px
@spacing-sm: @spacing-unit * 2;    // 16px
@spacing-md: @spacing-unit * 3;    // 24px
@spacing-lg: @spacing-unit * 4;    // 32px
@spacing-xl: @spacing-unit * 6;    // 48px

// 圆角
@border-radius-base: 4px;
@border-radius-sm: 2px;
@border-radius-lg: 8px;
@border-radius-round: 50%;

// 阴影
@shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
@shadow-base: 0 4px 8px rgba(0, 0, 0, 0.1);
@box-shadow-base: 0 2px 8px rgba(0, 0, 0, 0.15);
@shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
@shadow-xl: 0 16px 32px rgba(0, 0, 0, 0.1);

// 过渡
@transition-base: all 0.3s ease;
@transition-fast: all 0.2s ease;
@transition-slow: all 0.4s ease;

// 断点
@screen-xs: 480px;
@screen-sm: 576px;
@screen-md: 768px;
@screen-lg: 992px;
@screen-xl: 1200px;
@screen-xxl: 1600px;

// Z-index
@z-index-dropdown: 1000;
@z-index-sticky: 1020;
@z-index-fixed: 1030;
@z-index-modal-backdrop: 1040;
@z-index-modal: 1050;
@z-index-popover: 1060;
@z-index-tooltip: 1070;

// 桌面端特有变量
@titlebar-height: 32px;
@statusbar-height: 24px;
@sidebar-width: 200px;
@sidebar-collapsed-width: 64px;