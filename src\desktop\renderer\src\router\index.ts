/**
 * Desktop端路由配置
 */

import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'home',
    component: () => import('../views/HomeView.vue'),
    meta: {
      title: '首页',
      keepAlive: true
    }
  },
  {
    path: '/dashboard',
    name: 'dashboard',
    component: () => import('../views/DashboardView.vue'),
    meta: {
      title: '仪表板',
      keepAlive: true
    }
  },
  {
    path: '/examples',
    name: 'examples',
    component: () => import('../views/ExamplesView.vue'),
    meta: {
      title: '示例',
      keepAlive: true
    },
    children: [
      {
        path: 'storage',
        name: 'storage-example',
        component: () => import('../views/examples/StorageExample.vue'),
        meta: {
          title: '存储示例'
        }
      },
      {
        path: 'window',
        name: 'window-example',
        component: () => import('../views/examples/WindowExample.vue'),
        meta: {
          title: '窗口示例'
        }
      },
      {
        path: 'notification',
        name: 'notification-example',
        component: () => import('../views/examples/NotificationExample.vue'),
        meta: {
          title: '通知示例'
        }
      },
      {
        path: 'theme',
        name: 'theme-example',
        component: () => import('../views/examples/ThemeExample.vue'),
        meta: {
          title: '主题示例'
        }
      },
      {
        path: 'network',
        name: 'network-example',
        component: () => import('../views/examples/NetworkExample.vue'),
        meta: {
          title: '网络示例'
        }
      },
      {
        path: 'clipboard',
        name: 'clipboard-example',
        component: () => import('../views/examples/ClipboardExample.vue'),
        meta: {
          title: '剪贴板示例'
        }
      },
      {
        path: 'iframe',
        name: 'iframe-example',
        component: () => import('../views/examples/IframeExample.vue'),
        meta: {
          title: '嵌入示例'
        }
      }
    ]
  },
  {
    path: '/settings',
    name: 'settings',
    component: () => import('../views/SettingsView.vue'),
    meta: {
      title: '设置',
      keepAlive: true
    }
  },
  {
    path: '/fullscreen',
    name: 'fullscreen',
    component: () => import('../views/FullscreenView.vue'),
    meta: {
      title: '全屏模式',
      hideTitlebar: true,
      hideStatusbar: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('../views/NotFoundView.vue'),
    meta: {
      title: '页面未找到'
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - Team AI 2.0`
  }

  // 设置窗口标题（Desktop端）
  if (window.electronAPI && to.meta.title) {
    document.title = `${to.meta.title} - Team AI 2.0`
  }

  next()
})

router.afterEach(() => {
  // 可以在这里添加页面切换后的逻辑
})

export default router