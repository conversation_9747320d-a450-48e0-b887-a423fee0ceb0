<template>
  <div class="dashboard-view">
    <div class="dashboard-header">
      <h2>仪表板</h2>
      <div class="header-actions">
        <a-button type="primary" @click="refreshData" :loading="loading">
          <a-icon type="reload" />
          刷新
        </a-button>
      </div>
    </div>

    <div class="dashboard-grid">
      <!-- 系统状态 -->
      <div class="dashboard-card">
        <div class="card-header">
          <h3>系统状态</h3>
          <a-tag :color="systemStatus.color">{{ systemStatus.text }}</a-tag>
        </div>
        <div class="card-content">
          <div class="status-item">
            <span class="label">CPU 使用率</span>
            <div class="progress-container">
              <a-progress :percent="cpuUsage" :show-info="true" />
            </div>
          </div>
          <div class="status-item">
            <span class="label">内存使用率</span>
            <div class="progress-container">
              <a-progress :percent="memoryUsage" :show-info="true" />
            </div>
          </div>
          <div class="status-item">
            <span class="label">磁盘使用率</span>
            <div class="progress-container">
              <a-progress :percent="diskUsage" :show-info="true" />
            </div>
          </div>
        </div>
      </div>

      <!-- 网络状态 -->
      <div class="dashboard-card">
        <div class="card-header">
          <h3>网络状态</h3>
          <a-tag :color="networkStatus.color">{{ networkStatus.text }}</a-tag>
        </div>
        <div class="card-content">
          <div class="status-item">
            <span class="label">连接状态</span>
            <span class="value">{{ networkStatus.online ? '在线' : '离线' }}</span>
          </div>
          <div class="status-item">
            <span class="label">IP 地址</span>
            <span class="value">{{ ipAddress || '获取中...' }}</span>
          </div>
          <div class="status-item">
            <span class="label">连接类型</span>
            <span class="value">{{ connectionType || '未知' }}</span>
          </div>
        </div>
      </div>

      <!-- 电池状态 -->
      <div class="dashboard-card" v-if="batteryInfo">
        <div class="card-header">
          <h3>电池状态</h3>
          <a-tag :color="batteryStatus.color">{{ batteryStatus.text }}</a-tag>
        </div>
        <div class="card-content">
          <div class="status-item">
            <span class="label">电量</span>
            <div class="progress-container">
              <a-progress :percent="batteryInfo.level" :show-info="true" />
            </div>
          </div>
          <div class="status-item">
            <span class="label">状态</span>
            <span class="value">{{ batteryInfo.isCharging ? '充电中' : '使用电池' }}</span>
          </div>
          <div class="status-item">
            <span class="label">剩余时间</span>
            <span class="value">{{ batteryTimeRemaining }}</span>
          </div>
        </div>
      </div>

      <!-- 应用信息 -->
      <div class="dashboard-card">
        <div class="card-header">
          <h3>应用信息</h3>
        </div>
        <div class="card-content">
          <div class="status-item">
            <span class="label">应用名称</span>
            <span class="value">{{ appName }}</span>
          </div>
          <div class="status-item">
            <span class="label">应用版本</span>
            <span class="value">{{ appVersion }}</span>
          </div>
          <div class="status-item">
            <span class="label">运行时间</span>
            <span class="value">{{ uptime }}</span>
          </div>
          <div class="status-item">
            <span class="label">进程数</span>
            <span class="value">{{ processCount }}</span>
          </div>
        </div>
      </div>

      <!-- 快速操作 -->
      <div class="dashboard-card">
        <div class="card-header">
          <h3>快速操作</h3>
        </div>
        <div class="card-content">
          <div class="quick-actions">
            <a-button type="primary" @click="openSettings" block>
              <a-icon type="setting" />
              设置
            </a-button>
            <a-button @click="openExamples" block>
              <a-icon type="code" />
              示例
            </a-button>
            <a-button @click="openFullscreen" block>
              <a-icon type="fullscreen" />
              全屏模式
            </a-button>
            <a-button @click="openLogs" block>
              <a-icon type="file-text" />
              查看日志
            </a-button>
          </div>
        </div>
      </div>

      <!-- 最近活动 -->
      <div class="dashboard-card">
        <div class="card-header">
          <h3>最近活动</h3>
        </div>
        <div class="card-content">
          <div class="activity-list">
            <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
              <div class="activity-icon">
                <a-icon :type="activity.icon" />
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-time">{{ activity.time }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'

const router = useRouter()

// 状态
const loading = ref(false)
const cpuUsage = ref(0)
const memoryUsage = ref(0)
const diskUsage = ref(0)
const networkStatus = ref({ online: false, text: '离线', color: 'red' })
const ipAddress = ref('')
const connectionType = ref('')
const batteryInfo = ref<any>(null)
const batteryStatus = ref({ text: '未知', color: 'default' })
const batteryTimeRemaining = ref('')
const appName = ref('Team AI 2.0')
const appVersion = ref('1.0.0')
const uptime = ref('0 分钟')
const processCount = ref(1)
const systemStatus = ref({ text: '正常', color: 'green' })
const recentActivities = ref([
  { id: 1, title: '应用启动', time: '刚刚', icon: 'poweroff' },
  { id: 2, title: '检查更新', time: '1分钟前', icon: 'sync' },
  { id: 3, title: '加载配置', time: '2分钟前', icon: 'setting' },
  { id: 4, title: '初始化服务', time: '3分钟前', icon: 'api' }
])

// 方法
const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadSystemStatus(),
      loadNetworkStatus(),
      loadBatteryInfo(),
      loadAppInfo()
    ])
    message.success('数据刷新成功')
  } catch (error) {
    message.error('数据刷新失败')
  } finally {
    loading.value = false
  }
}

const loadSystemStatus = async () => {
  // 模拟系统状态数据
  cpuUsage.value = Math.floor(Math.random() * 100)
  memoryUsage.value = Math.floor(Math.random() * 100)
  diskUsage.value = Math.floor(Math.random() * 100)

  if (cpuUsage.value > 80 || memoryUsage.value > 80) {
    systemStatus.value = { text: '警告', color: 'orange' }
  } else {
    systemStatus.value = { text: '正常', color: 'green' }
  }
}

const loadNetworkStatus = async () => {
  networkStatus.value.online = navigator.onLine
  networkStatus.value.text = networkStatus.value.online ? '在线' : '离线'
  networkStatus.value.color = networkStatus.value.online ? 'green' : 'red'

  // 获取IP地址
  try {
    const response = await fetch('https://api.ipify.org?format=json')
    const data = await response.json()
    ipAddress.value = data.ip
  } catch (error) {
    ipAddress.value = '获取失败'
  }

  // 获取连接类型
  connectionType.value = (navigator as any).connection?.effectiveType || '未知'
}

const loadBatteryInfo = async () => {
  if (window.electronAPI) {
    try {
      batteryInfo.value = await window.electronAPI.power.getBatteryInfo()

      if (batteryInfo.value) {
        // 更新电池状态
        if (batteryInfo.value.level > 50) {
          batteryStatus.value = { text: '良好', color: 'green' }
        } else if (batteryInfo.value.level > 20) {
          batteryStatus.value = { text: '正常', color: 'orange' }
        } else {
          batteryStatus.value = { text: '低电量', color: 'red' }
        }

        // 计算剩余时间
        if (batteryInfo.value.isCharging) {
          batteryTimeRemaining.value = '充电中'
        } else {
          const hours = Math.floor(batteryInfo.value.level / 10)
          batteryTimeRemaining.value = `约 ${hours} 小时`
        }
      }
    } catch (error) {
      console.error('获取电池信息失败:', error)
    }
  }
}

const loadAppInfo = async () => {
  if (window.electronAPI) {
    try {
      appName.value = await window.electronAPI.app.getName()
      appVersion.value = await window.electronAPI.app.getVersion()
    } catch (error) {
      console.error('获取应用信息失败:', error)
    }
  }

  // 计算运行时间
  const startTime = Date.now()
  const updateUptime = () => {
    const elapsed = Date.now() - startTime
    const minutes = Math.floor(elapsed / 60000)
    const hours = Math.floor(minutes / 60)

    if (hours > 0) {
      uptime.value = `${hours} 小时 ${minutes % 60} 分钟`
    } else {
      uptime.value = `${minutes} 分钟`
    }
  }

  setInterval(updateUptime, 60000) // 每分钟更新一次
  updateUptime()
}

const openSettings = () => {
  router.push('/settings')
}

const openExamples = () => {
  router.push('/examples')
}

const openFullscreen = () => {
  router.push('/fullscreen')
}

const openLogs = () => {
  // TODO: 打开日志页面
  message.info('日志功能开发中')
}

// 生命周期
onMounted(() => {
  refreshData()

  // 定时刷新数据
  const refreshInterval = setInterval(() => {
    loadSystemStatus()
    loadNetworkStatus()
    loadBatteryInfo()
  }, 30000) // 每30秒刷新一次

  onUnmounted(() => {
    clearInterval(refreshInterval)
  })
})
</script>

<style lang="less" scoped>
.dashboard-view {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  .dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: #333;
    }
  }

  .dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 24px;

    .dashboard-card {
      background: #fff;
      border-radius: 8px;
      border: 1px solid #f0f0f0;
      overflow: hidden;

      .card-header {
        padding: 16px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        justify-content: space-between;
        align-items: center;

        h3 {
          font-size: 16px;
          font-weight: 600;
          color: #333;
          margin: 0;
        }
      }

      .card-content {
        padding: 16px;

        .status-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 12px;

          &:last-child {
            margin-bottom: 0;
          }

          .label {
            font-size: 14px;
            color: #666;
            min-width: 80px;
          }

          .value {
            font-size: 14px;
            font-weight: 500;
            color: #333;
          }

          .progress-container {
            flex: 1;
            margin-left: 16px;
          }
        }

        .quick-actions {
          display: flex;
          flex-direction: column;
          gap: 8px;

          .ant-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
          }
        }

        .activity-list {
          .activity-item {
            display: flex;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f0f0f0;

            &:last-child {
              border-bottom: none;
            }

            .activity-icon {
              font-size: 16px;
              color: #1890ff;
              margin-right: 12px;
            }

            .activity-content {
              flex: 1;

              .activity-title {
                font-size: 14px;
                color: #333;
                margin-bottom: 2px;
              }

              .activity-time {
                font-size: 12px;
                color: #999;
              }
            }
          }
        }
      }
    }
  }
}

// 深色主题
[data-theme="dark"] {
  .dashboard-view {
    .dashboard-header {
      h2 {
        color: #fff;
      }
    }

    .dashboard-card {
      background: #1f1f1f;
      border-color: #303030;

      .card-header {
        border-color: #303030;

        h3 {
          color: #fff;
        }
      }

      .card-content {
        .status-item {
          .label {
            color: #bfbfbf;
          }

          .value {
            color: #fff;
          }
        }

        .activity-list {
          .activity-item {
            border-color: #303030;

            .activity-content {
              .activity-title {
                color: #fff;
              }

              .activity-time {
                color: #666;
              }
            }
          }
        }
      }
    }
  }
}
</style>