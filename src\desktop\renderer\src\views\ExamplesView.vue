<template>
  <div class="examples-view">
    <div class="examples-header">
      <h2>功能示例</h2>
      <p>探索 Team AI 2.0 框架的各种功能特性</p>
    </div>

    <div class="examples-grid">
      <div class="example-card" @click="navigateTo('storage')">
        <div class="example-icon">
          <a-icon type="database" />
        </div>
        <h3>存储示例</h3>
        <p>演示数据存储和管理功能</p>
        <a-button type="primary">查看示例</a-button>
      </div>

      <div class="example-card" @click="navigateTo('window')">
        <div class="example-icon">
          <a-icon type="windows" />
        </div>
        <h3>窗口示例</h3>
        <p>演示窗口管理和操作功能</p>
        <a-button type="primary">查看示例</a-button>
      </div>

      <div class="example-card" @click="navigateTo('notification')">
        <div class="example-icon">
          <a-icon type="bell" />
        </div>
        <h3>通知示例</h3>
        <p>演示通知和消息功能</p>
        <a-button type="primary">查看示例</a-button>
      </div>

      <div class="example-card" @click="navigateTo('theme')">
        <div class="example-icon">
          <a-icon type="skin" />
        </div>
        <h3>主题示例</h3>
        <p>演示主题切换和样式功能</p>
        <a-button type="primary">查看示例</a-button>
      </div>

      <div class="example-card" @click="navigateTo('network')">
        <div class="example-icon">
          <a-icon type="wifi" />
        </div>
        <h3>网络示例</h3>
        <p>演示网络请求和数据传输功能</p>
        <a-button type="primary">查看示例</a-button>
      </div>

      <div class="example-card" @click="navigateTo('clipboard')">
        <div class="example-icon">
          <a-icon type="copy" />
        </div>
        <h3>剪贴板示例</h3>
        <p>演示剪贴板操作功能</p>
        <a-button type="primary">查看示例</a-button>
      </div>

      <div class="example-card" @click="navigateTo('iframe')">
        <div class="example-icon">
          <a-icon type="global" />
        </div>
        <h3>嵌入示例</h3>
        <p>演示 iframe 嵌入和通信功能</p>
        <a-button type="primary">查看示例</a-button>
      </div>
    </div>

    <div class="examples-info">
      <h3>使用说明</h3>
      <ul>
        <li>每个示例都展示了框架的核心功能</li>
        <li>代码示例可以直接复制到项目中使用</li>
        <li>支持响应式设计，适配不同屏幕尺寸</li>
        <li>所有功能都经过安全性和性能优化</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

// 方法
const navigateTo = (path: string) => {
  router.push(`/examples/${path}`)
}
</script>

<style lang="less" scoped>
.examples-view {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  .examples-header {
    text-align: center;
    margin-bottom: 48px;

    h2 {
      font-size: 28px;
      font-weight: 600;
      margin-bottom: 12px;
      color: #333;
    }

    p {
      font-size: 16px;
      color: #666;
    }
  }

  .examples-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    margin-bottom: 48px;

    .example-card {
      background: #fff;
      border-radius: 8px;
      padding: 24px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid #f0f0f0;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-color: #1890ff;
      }

      .example-icon {
        font-size: 48px;
        color: #1890ff;
        margin-bottom: 16px;
      }

      h3 {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 8px;
        color: #333;
      }

      p {
        font-size: 14px;
        color: #666;
        margin-bottom: 16px;
        line-height: 1.5;
      }

      .ant-btn {
        min-width: 120px;
      }
    }
  }

  .examples-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 24px;

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 16px;
      color: #333;
    }

    ul {
      list-style: none;
      padding: 0;

      li {
        font-size: 14px;
        color: #666;
        margin-bottom: 8px;
        padding-left: 20px;
        position: relative;

        &:before {
          content: '✓';
          position: absolute;
          left: 0;
          color: #52c41a;
          font-weight: bold;
        }
      }
    }
  }
}

// 深色主题
[data-theme="dark"] {
  .examples-view {
    .examples-header {
      h2 {
        color: #fff;
      }

      p {
        color: #bfbfbf;
      }
    }

    .example-card {
      background: #1f1f1f;
      border-color: #303030;

      &:hover {
        border-color: #40a9ff;
      }

      h3 {
        color: #fff;
      }

      p {
        color: #bfbfbf;
      }
    }

    .examples-info {
      background: #2d2d2d;

      h3 {
        color: #fff;
      }

      ul {
        li {
          color: #bfbfbf;
        }
      }
    }
  }
}
</style>