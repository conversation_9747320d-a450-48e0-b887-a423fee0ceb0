<template>
  <div class="fullscreen-view">
    <div class="fullscreen-content">
      <div class="fullscreen-header">
        <h1>全屏模式</h1>
        <a-button type="primary" @click="exitFullscreen">
          <a-icon type="fullscreen-exit" />
          退出全屏
        </a-button>
      </div>

      <div class="fullscreen-body">
        <div class="demo-content">
          <h2>Team AI 2.0 全屏演示</h2>
          <p>这是一个全屏模式的演示页面，展示了应用程序在全屏状态下的显示效果。</p>

          <div class="feature-showcase">
            <div class="showcase-item">
              <div class="showcase-icon">
                <a-icon type="dashboard" />
              </div>
              <h3>完整体验</h3>
              <p>全屏模式下提供完整的用户体验，无干扰界面</p>
            </div>

            <div class="showcase-item">
              <div class="showcase-icon">
                <a-icon type="picture" />
              </div>
              <h3>沉浸式</h3>
              <p>最大化利用屏幕空间，提供沉浸式体验</p>
            </div>

            <div class="showcase-item">
              <div class="showcase-icon">
                <a-icon type="keyboard" />
              </div>
              <h3>快捷键支持</h3>
              <p>支持键盘快捷键操作，提升使用效率</p>
            </div>
          </div>

          <div class="interactive-demo">
            <h3>交互演示</h3>
            <div class="demo-controls">
              <a-button type="primary" @click="showMessage">显示消息</a-button>
              <a-button @click="changeTheme">切换主题</a-button>
              <a-button @click="openModal">打开模态框</a-button>
            </div>
          </div>

          <div class="stats-display">
            <h3>实时信息</h3>
            <div class="stats-grid">
              <div class="stat-item">
                <span class="stat-label">时间</span>
                <span class="stat-value">{{ currentTime }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">分辨率</span>
                <span class="stat-value">{{ resolution }}</span>
              </div>
              <div class="stat-item">
                <span class="stat-label">状态</span>
                <span class="stat-value">
                  <a-tag :color="statusColor">{{ statusText }}</a-tag>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 模态框 -->
    <a-modal v-model:visible="modalVisible" title="演示模态框" @ok="handleModalOk">
      <p>这是一个在全屏模式下打开的模态框示例。</p>
      <p>模态框在全屏模式下仍然正常工作，提供良好的用户体验。</p>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
// import { ThemeService } from '@core/services'

const router = useRouter()

// 状态
const currentTime = ref('')
const resolution = ref('')
const statusText = ref('正常')
const statusColor = ref('green')
const modalVisible = ref(false)
const themeService = new ThemeService()

// 方法
const exitFullscreen = () => {
  router.push('/')
}

const showMessage = () => {
  message.success('这是一个演示消息！')
  message.info('全屏模式下的消息显示正常')
  message.warning('警告消息测试')
  message.error('错误消息测试')
}

const changeTheme = async () => {
  await themeService.toggleTheme()
  message.success('主题已切换')
}

const openModal = () => {
  modalVisible.value = true
}

const handleModalOk = () => {
  modalVisible.value = false
  message.success('模态框已关闭')
}

const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString()
}

const updateResolution = () => {
  resolution.value = `${window.screen.width} × ${window.screen.height}`
}

const updateStatus = () => {
  // 模拟状态变化
  const statuses = [
    { text: '正常', color: 'green' },
    { text: '忙碌', color: 'orange' },
    { text: '离线', color: 'red' }
  ]

  const randomStatus = statuses[Math.floor(Math.random() * statuses.length)]
  statusText.value = randomStatus.text
  statusColor.value = randomStatus.color
}

// 键盘事件处理
const handleKeyDown = (event: KeyboardEvent) => {
  // ESC 键退出全屏
  if (event.key === 'Escape') {
    exitFullscreen()
  }

  // Ctrl/Cmd + W 关闭窗口
  if ((event.ctrlKey || event.metaKey) && event.key === 'w') {
    event.preventDefault()
    exitFullscreen()
  }

  // Ctrl/Cmd + M 最小化窗口
  if ((event.ctrlKey || event.metaKey) && event.key === 'm') {
    event.preventDefault()
    if (window.electronAPI) {
      window.electronAPI.window.minimize()
    }
  }

  // F1 显示帮助
  if (event.key === 'F1') {
    event.preventDefault()
    message.info('快捷键帮助：\nESC - 退出全屏\nCtrl+W - 退出全屏\nCtrl+M - 最小化窗口\nF1 - 显示帮助')
  }
}

// 生命周期
onMounted(() => {
  // 初始化时间显示
  updateTime()
  updateResolution()

  // 设置定时器
  const timeInterval = setInterval(updateTime, 1000)
  const statusInterval = setInterval(updateStatus, 5000)

  // 监听键盘事件
  window.addEventListener('keydown', handleKeyDown)

  // 监听窗口大小变化
  window.addEventListener('resize', updateResolution)

  // 清理函数
  onUnmounted(() => {
    clearInterval(timeInterval)
    clearInterval(statusInterval)
    window.removeEventListener('keydown', handleKeyDown)
    window.removeEventListener('resize', updateResolution)
  })
})
</script>

<style lang="less" scoped>
.fullscreen-view {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  .fullscreen-content {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
  }

  .fullscreen-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 40px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);

    h1 {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }

  .fullscreen-body {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    overflow-y: auto;

    .demo-content {
      max-width: 800px;
      width: 100%;
      text-align: center;

      h2 {
        font-size: 32px;
        font-weight: 600;
        color: #333;
        margin-bottom: 16px;
      }

      p {
        font-size: 16px;
        color: #666;
        margin-bottom: 40px;
        line-height: 1.6;
      }

      .feature-showcase {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 24px;
        margin-bottom: 40px;

        .showcase-item {
          background: #fff;
          border-radius: 12px;
          padding: 24px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          transition: transform 0.3s ease;

          &:hover {
            transform: translateY(-4px);
          }

          .showcase-icon {
            font-size: 48px;
            color: #1890ff;
            margin-bottom: 16px;
          }

          h3 {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
          }

          p {
            font-size: 14px;
            color: #666;
            margin-bottom: 0;
          }
        }
      }

      .interactive-demo {
        background: #fff;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        margin-bottom: 40px;

        h3 {
          font-size: 20px;
          font-weight: 600;
          color: #333;
          margin-bottom: 16px;
        }

        .demo-controls {
          display: flex;
          gap: 12px;
          justify-content: center;
          flex-wrap: wrap;
        }
      }

      .stats-display {
        background: #fff;
        border-radius: 12px;
        padding: 24px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

        h3 {
          font-size: 20px;
          font-weight: 600;
          color: #333;
          margin-bottom: 16px;
        }

        .stats-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: 16px;

          .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;

            .stat-label {
              font-size: 14px;
              color: #666;
              margin-bottom: 8px;
            }

            .stat-value {
              font-size: 16px;
              font-weight: 600;
              color: #333;
            }
          }
        }
      }
    }
  }
}

// 深色主题
[data-theme="dark"] {
  .fullscreen-view {
    background: linear-gradient(135deg, #1f1f1f 0%, #303030 100%);

    .fullscreen-content {
      background: rgba(31, 31, 31, 0.95);
    }

    .fullscreen-header {
      background: rgba(0, 0, 0, 0.2);
      border-bottom-color: rgba(255, 255, 255, 0.1);

      h1 {
        color: #fff;
      }
    }

    .fullscreen-body {
      .demo-content {
        h2 {
          color: #fff;
        }

        p {
          color: #bfbfbf;
        }

        .feature-showcase {
          .showcase-item {
            background: #2d2d2d;

            h3 {
              color: #fff;
            }

            p {
              color: #bfbfbf;
            }
          }
        }

        .interactive-demo {
          background: #2d2d2d;

          h3 {
            color: #fff;
          }
        }

        .stats-display {
          background: #2d2d2d;

          h3 {
            color: #fff;
          }

          .stat-item {
            .stat-label {
              color: #bfbfbf;
            }

            .stat-value {
              color: #fff;
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .fullscreen-view {
    .fullscreen-header {
      padding: 16px 20px;

      h1 {
        font-size: 20px;
      }
    }

    .fullscreen-body {
      padding: 20px;

      .demo-content {
        h2 {
          font-size: 24px;
        }

        .feature-showcase {
          grid-template-columns: 1fr;
        }

        .stats-grid {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}
</style>