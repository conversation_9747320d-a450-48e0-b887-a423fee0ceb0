<template>
  <div class="home-view">
    <div class="welcome-section">
      <h1>欢迎使用 Team AI 2.0</h1>
      <p>基于 Vue3 和 Electron 的跨平台应用框架</p>
    </div>

    <div class="feature-grid">
      <div class="feature-card" @click="navigateTo('/dashboard')">
        <div class="feature-icon">
          <a-icon type="dashboard" />
        </div>
        <h3>仪表板</h3>
        <p>查看系统状态和概览信息</p>
      </div>

      <div class="feature-card" @click="navigateTo('/examples')">
        <div class="feature-icon">
          <a-icon type="code" />
        </div>
        <h3>功能示例</h3>
        <p>探索框架的各种功能特性</p>
      </div>

      <div class="feature-card" @click="navigateTo('/settings')">
        <div class="feature-icon">
          <a-icon type="setting" />
        </div>
        <h3>设置</h3>
        <p>配置应用程序选项</p>
      </div>

      <div class="feature-card" @click="openFullscreen">
        <div class="feature-icon">
          <a-icon type="fullscreen" />
        </div>
        <h3>全屏模式</h3>
        <p>进入全屏显示模式</p>
      </div>
    </div>

    <div class="system-info">
      <h3>系统信息</h3>
      <div class="info-grid">
        <div class="info-item">
          <span class="label">平台:</span>
          <span class="value">{{ platform }}</span>
        </div>
        <div class="info-item">
          <span class="label">环境:</span>
          <span class="value">{{ environment }}</span>
        </div>
        <div class="info-item">
          <span class="label">版本:</span>
          <span class="value">{{ version }}</span>
        </div>
        <div class="info-item">
          <span class="label">应用版本:</span>
          <span class="value">{{ appVersion }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'

const router = useRouter()

// 状态
const platform = ref('unknown')
const environment = ref('unknown')
const version = ref('unknown')
const appVersion = ref('unknown')

// 方法
const navigateTo = (path: string) => {
  router.push(path)
}

const openFullscreen = () => {
  router.push('/fullscreen')
}

const loadSystemInfo = async () => {
  try {
    // 获取平台信息
    if (window.appUtils) {
      platform.value = window.appUtils.platform
      environment.value = window.appUtils.getEnvironment()
      version.value = window.appUtils.version
    }

    // 获取应用版本
    if (window.electronAPI) {
      appVersion.value = await window.electronAPI.app.getVersion()
    }
  } catch (error) {
    console.error('加载系统信息失败:', error)
  }
}

// 生命周期
onMounted(() => {
  loadSystemInfo()
})
</script>

<style lang="less" scoped>
.home-view {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;

  .welcome-section {
    text-align: center;
    margin-bottom: 48px;

    h1 {
      font-size: 32px;
      font-weight: 600;
      margin-bottom: 12px;
      color: #1890ff;
    }

    p {
      font-size: 16px;
      color: #666;
    }
  }

  .feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 48px;

    .feature-card {
      background: #fff;
      border-radius: 8px;
      padding: 24px;
      text-align: center;
      cursor: pointer;
      transition: all 0.3s ease;
      border: 1px solid #f0f0f0;

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        border-color: #1890ff;
      }

      .feature-icon {
        font-size: 48px;
        color: #1890ff;
        margin-bottom: 16px;
      }

      h3 {
        font-size: 18px;
        font-weight: 600;
        margin-bottom: 8px;
        color: #333;
      }

      p {
        font-size: 14px;
        color: #666;
        line-height: 1.5;
      }
    }
  }

  .system-info {
    background: #fff;
    border-radius: 8px;
    padding: 24px;
    border: 1px solid #f0f0f0;

    h3 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 16px;
      color: #333;
    }

    .info-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 16px;

      .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .label {
          font-size: 14px;
          color: #666;
        }

        .value {
          font-size: 14px;
          font-weight: 500;
          color: #333;
        }
      }
    }
  }
}

// 深色主题
[data-theme="dark"] {
  .home-view {
    .welcome-section {
      h1 {
        color: #40a9ff;
      }

      p {
        color: #bfbfbf;
      }
    }

    .feature-card {
      background: #1f1f1f;
      border-color: #303030;

      &:hover {
        border-color: #40a9ff;
      }

      h3 {
        color: #fff;
      }

      p {
        color: #bfbfbf;
      }
    }

    .system-info {
      background: #1f1f1f;
      border-color: #303030;

      h3 {
        color: #fff;
      }

      .info-item {
        .label {
          color: #bfbfbf;
        }

        .value {
          color: #fff;
        }
      }
    }
  }
}
</style>