<template>
  <div class="not-found-view">
    <div class="not-found-content">
      <div class="error-icon">
        <a-icon type="warning" />
      </div>
      <h1>404</h1>
      <h2>页面未找到</h2>
      <p>抱歉，您访问的页面不存在或已被移除。</p>

      <div class="actions">
        <a-button type="primary" @click="goHome">
          <a-icon type="home" />
          返回首页
        </a-button>
        <a-button @click="goBack">
          <a-icon type="arrow-left" />
          返回上页
        </a-button>
      </div>

      <div class="help-section">
        <h3>您可能想要：</h3>
        <ul>
          <li>
            <a @click="navigateTo('/dashboard')">仪表板</a>
            <span>查看系统概览和状态</span>
          </li>
          <li>
            <a @click="navigateTo('/examples')">功能示例</a>
            <span>探索框架功能</span>
          </li>
          <li>
            <a @click="navigateTo('/settings')">设置</a>
            <span>配置应用选项</span>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

// 方法
const goHome = () => {
  router.push('/')
}

const goBack = () => {
  router.go(-1)
}

const navigateTo = (path: string) => {
  router.push(path)
}
</script>

<style lang="less" scoped>
.not-found-view {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: #f5f5f5;

  .not-found-content {
    text-align: center;
    max-width: 500px;
    padding: 40px;

    .error-icon {
      font-size: 80px;
      color: #faad14;
      margin-bottom: 20px;
    }

    h1 {
      font-size: 72px;
      font-weight: 700;
      color: #333;
      margin: 0 0 16px 0;
    }

    h2 {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin: 0 0 16px 0;
    }

    p {
      font-size: 16px;
      color: #666;
      margin-bottom: 32px;
      line-height: 1.6;
    }

    .actions {
      display: flex;
      gap: 16px;
      justify-content: center;
      margin-bottom: 48px;

      .ant-btn {
        display: flex;
        align-items: center;
        gap: 8px;
        min-width: 120px;
      }
    }

    .help-section {
      background: #fff;
      border-radius: 8px;
      padding: 24px;
      text-align: left;

      h3 {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 16px;
      }

      ul {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          a {
            font-size: 14px;
            color: #1890ff;
            text-decoration: none;
            cursor: pointer;
            margin-right: 8px;
            min-width: 80px;

            &:hover {
              text-decoration: underline;
            }
          }

          span {
            font-size: 14px;
            color: #666;
          }
        }
      }
    }
  }
}

// 深色主题
[data-theme="dark"] {
  .not-found-view {
    background: #1f1f1f;

    .not-found-content {
      h1, h2 {
        color: #fff;
      }

      p {
        color: #bfbfbf;
      }

      .help-section {
        background: #2d2d2d;

        h3 {
          color: #fff;
        }

        ul {
          li {
            span {
              color: #bfbfbf;
            }
          }
        }
      }
    }
  }
}
</style>