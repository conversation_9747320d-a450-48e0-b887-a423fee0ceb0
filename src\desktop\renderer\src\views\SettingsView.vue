<template>
  <div class="settings-view">
    <div class="settings-header">
      <h2>设置</h2>
      <p>配置应用程序选项</p>
    </div>

    <div class="settings-content">
      <a-tabs v-model:activeKey="activeTab" type="card">
        <!-- 通用设置 -->
        <a-tab-pane key="general" tab="通用">
          <div class="settings-section">
            <h3>通用设置</h3>

            <div class="setting-item">
              <div class="setting-label">
                <span>主题</span>
                <span class="setting-description">选择应用程序主题</span>
              </div>
              <div class="setting-control">
                <a-select v-model:value="settings.theme" style="width: 120px" @change="handleThemeChange">
                  <a-select-option value="light">浅色</a-select-option>
                  <a-select-option value="dark">深色</a-select-option>
                  <a-select-option value="auto">自动</a-select-option>
                </a-select>
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-label">
                <span>语言</span>
                <span class="setting-description">选择界面语言</span>
              </div>
              <div class="setting-control">
                <a-select v-model:value="settings.language" style="width: 120px">
                  <a-select-option value="zh-CN">中文</a-select-option>
                  <a-select-option value="en-US">English</a-select-option>
                </a-select>
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-label">
                <span>启动时最小化</span>
                <span class="setting-description">应用程序启动时自动最小化</span>
              </div>
              <div class="setting-control">
                <a-switch v-model:checked="settings.startMinimized" />
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-label">
                <span>开机自启动</span>
                <span class="setting-description">系统启动时自动运行应用程序</span>
              </div>
              <div class="setting-control">
                <a-switch v-model:checked="settings.autoStart" />
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 窗口设置 -->
        <a-tab-pane key="window" tab="窗口">
          <div class="settings-section">
            <h3>窗口设置</h3>

            <div class="setting-item">
              <div class="setting-label">
                <span>记住窗口大小</span>
                <span class="setting-description">保存并恢复窗口大小</span>
              </div>
              <div class="setting-control">
                <a-switch v-model:checked="settings.rememberWindowSize" />
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-label">
                <span>记住窗口位置</span>
                <span class="setting-description">保存并恢复窗口位置</span>
              </div>
              <div class="setting-control">
                <a-switch v-model:checked="settings.rememberWindowPosition" />
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-label">
                <span>默认宽度</span>
                <span class="setting-description">窗口默认宽度（像素）</span>
              </div>
              <div class="setting-control">
                <a-input-number v-model:value="settings.defaultWidth" :min="800" :max="1920" style="width: 120px" />
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-label">
                <span>默认高度</span>
                <span class="setting-description">窗口默认高度（像素）</span>
              </div>
              <div class="setting-control">
                <a-input-number v-model:value="settings.defaultHeight" :min="600" :max="1080" style="width: 120px" />
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 通知设置 -->
        <a-tab-pane key="notification" tab="通知">
          <div class="settings-section">
            <h3>通知设置</h3>

            <div class="setting-item">
              <div class="setting-label">
                <span>启用通知</span>
                <span class="setting-description">显示系统通知</span>
              </div>
              <div class="setting-control">
                <a-switch v-model:checked="settings.enableNotifications" />
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-label">
                <span>通知声音</span>
                <span class="setting-description">播放通知声音</span>
              </div>
              <div class="setting-control">
                <a-switch v-model:checked="settings.notificationSound" />
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-label">
                <span>免打扰模式</span>
                <span class="setting-description">在指定时间内不显示通知</span>
              </div>
              <div class="setting-control">
                <a-switch v-model:checked="settings.doNotDisturb" />
              </div>
            </div>

            <div class="setting-item" v-if="settings.doNotDisturb">
              <div class="setting-label">
                <span>免打扰时间</span>
                <span class="setting-description">设置免打扰时间段</span>
              </div>
              <div class="setting-control">
                <a-time-picker v-model:value="settings.doNotDisturbStart" format="HH:mm" style="width: 120px" />
                <span style="margin: 0 8px">至</span>
                <a-time-picker v-model:value="settings.doNotDisturbEnd" format="HH:mm" style="width: 120px" />
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 网络设置 -->
        <a-tab-pane key="network" tab="网络">
          <div class="settings-section">
            <h3>网络设置</h3>

            <div class="setting-item">
              <div class="setting-label">
                <span>代理服务器</span>
                <span class="setting-description">配置网络代理</span>
              </div>
              <div class="setting-control">
                <a-select v-model:value="settings.proxyMode" style="width: 120px" @change="handleProxyModeChange">
                  <a-select-option value="none">不使用代理</a-select-option>
                  <a-select-option value="system">系统代理</a-select-option>
                  <a-select-option value="manual">手动配置</a-select-option>
                </a-select>
              </div>
            </div>

            <div class="setting-item" v-if="settings.proxyMode === 'manual'">
              <div class="setting-label">
                <span>代理地址</span>
                <span class="setting-description">代理服务器地址</span>
              </div>
              <div class="setting-control">
                <a-input v-model:value="settings.proxyHost" placeholder="127.0.0.1" style="width: 200px" />
                <span style="margin: 0 8px">:</span>
                <a-input-number v-model:value="settings.proxyPort" :min="1" :max="65535" style="width: 80px" />
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-label">
                <span>超时时间</span>
                <span class="setting-description">网络请求超时时间（秒）</span>
              </div>
              <div class="setting-control">
                <a-input-number v-model:value="settings.timeout" :min="1" :max="300" style="width: 120px" />
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-label">
                <span>重试次数</span>
                <span class="setting-description">网络请求失败时重试次数</span>
              </div>
              <div class="setting-control">
                <a-input-number v-model:value="settings.retryCount" :min="0" :max="10" style="width: 120px" />
              </div>
            </div>
          </div>
        </a-tab-pane>

        <!-- 高级设置 -->
        <a-tab-pane key="advanced" tab="高级">
          <div class="settings-section">
            <h3>高级设置</h3>

            <div class="setting-item">
              <div class="setting-label">
                <span>开发模式</span>
                <span class="setting-description">启用开发者工具和调试功能</span>
              </div>
              <div class="setting-control">
                <a-switch v-model:checked="settings.devMode" />
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-label">
                <span>自动更新</span>
                <span class="setting-description">自动检查并安装更新</span>
              </div>
              <div class="setting-control">
                <a-switch v-model:checked="settings.autoUpdate" />
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-label">
                <span>日志级别</span>
                <span class="setting-description">设置应用程序日志级别</span>
              </div>
              <div class="setting-control">
                <a-select v-model:value="settings.logLevel" style="width: 120px">
                  <a-select-option value="debug">Debug</a-select-option>
                  <a-select-option value="info">Info</a-select-option>
                  <a-select-option value="warn">Warn</a-select-option>
                  <a-select-option value="error">Error</a-select-option>
                </a-select>
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-label">
                <span>缓存清理</span>
                <span class="setting-description">清理应用程序缓存数据</span>
              </div>
              <div class="setting-control">
                <a-button type="primary" @click="clearCache">清理缓存</a-button>
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-label">
                <span>数据导出</span>
                <span class="setting-description">导出应用程序数据</span>
              </div>
              <div class="setting-control">
                <a-button type="primary" @click="exportData">导出数据</a-button>
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-label">
                <span>重置设置</span>
                <span class="setting-description">恢复默认设置</span>
              </div>
              <div class="setting-control">
                <a-button type="danger" @click="resetSettings">重置设置</a-button>
              </div>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>

      <!-- 操作按钮 -->
      <div class="settings-actions">
        <a-button @click="resetSettings">重置</a-button>
        <a-button type="primary" @click="saveSettings" :loading="saving">
          保存设置
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'

// 状态
const activeTab = ref('general')
const saving = ref(false)

// 设置数据
const settings = reactive({
  // 通用设置
  theme: 'light',
  language: 'zh-CN',
  startMinimized: false,
  autoStart: false,

  // 窗口设置
  rememberWindowSize: true,
  rememberWindowPosition: true,
  defaultWidth: 1200,
  defaultHeight: 800,

  // 通知设置
  enableNotifications: true,
  notificationSound: true,
  doNotDisturb: false,
  doNotDisturbStart: null,
  doNotDisturbEnd: null,

  // 网络设置
  proxyMode: 'none',
  proxyHost: '',
  proxyPort: 8080,
  timeout: 30,
  retryCount: 3,

  // 高级设置
  devMode: false,
  autoUpdate: true,
  logLevel: 'info'
})

// 方法
const handleThemeChange = (value: string) => {
  // 应用主题
  document.documentElement.setAttribute('data-theme', value)
}

const handleProxyModeChange = (value: string) => {
  if (value !== 'manual') {
    settings.proxyHost = ''
    settings.proxyPort = 8080
  }
}

const saveSettings = async () => {
  saving.value = true
  try {
    // 保存设置到本地存储
    localStorage.setItem('settings', JSON.stringify(settings))

    // 应用设置
    handleThemeChange(settings.theme)

    message.success('设置保存成功')
  } catch (error) {
    message.error('设置保存失败')
  } finally {
    saving.value = false
  }
}

const resetSettings = () => {
  if (confirm('确定要重置所有设置吗？此操作不可撤销。')) {
    Object.assign(settings, {
      theme: 'light',
      language: 'zh-CN',
      startMinimized: false,
      autoStart: false,
      rememberWindowSize: true,
      rememberWindowPosition: true,
      defaultWidth: 1200,
      defaultHeight: 800,
      enableNotifications: true,
      notificationSound: true,
      doNotDisturb: false,
      doNotDisturbStart: null,
      doNotDisturbEnd: null,
      proxyMode: 'none',
      proxyHost: '',
      proxyPort: 8080,
      timeout: 30,
      retryCount: 3,
      devMode: false,
      autoUpdate: true,
      logLevel: 'info'
    })

    message.success('设置已重置')
  }
}

const clearCache = async () => {
  if (confirm('确定要清理缓存吗？此操作可能会影响应用程序性能。')) {
    try {
      // 清理本地存储
      localStorage.clear()

      // 清理缓存
      if ('caches' in window) {
        const cacheNames = await caches.keys()
        await Promise.all(cacheNames.map(cacheName => caches.delete(cacheName)))
      }

      message.success('缓存清理成功')
    } catch (error) {
      message.error('缓存清理失败')
    }
  }
}

const exportData = async () => {
  try {
    // 导出设置和数据
    const data = {
      settings: settings,
      timestamp: new Date().toISOString(),
      version: '1.0.0'
    }

    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
    const url = URL.createObjectURL(blob)

    const a = document.createElement('a')
    a.href = url
    a.download = `team-ai-settings-${new Date().toISOString().split('T')[0]}.json`
    a.click()

    URL.revokeObjectURL(url)

    message.success('数据导出成功')
  } catch (error) {
    message.error('数据导出失败')
  }
}

// 生命周期
onMounted(() => {
  // 加载设置
  try {
    const savedSettings = localStorage.getItem('settings')
    if (savedSettings) {
      Object.assign(settings, JSON.parse(savedSettings))
    }

    // 应用当前主题
    handleThemeChange(settings.theme)
  } catch (error) {
    console.error('加载设置失败:', error)
  }
})
</script>

<style lang="less" scoped>
.settings-view {
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;

  .settings-header {
    text-align: center;
    margin-bottom: 32px;

    h2 {
      font-size: 24px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #333;
    }

    p {
      font-size: 14px;
      color: #666;
    }
  }

  .settings-content {
    .settings-section {
      h3 {
        font-size: 16px;
        font-weight: 600;
        margin-bottom: 24px;
        color: #333;
      }

      .setting-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 24px;
        padding: 16px;
        background: #f8f9fa;
        border-radius: 6px;

        .setting-label {
          flex: 1;

          span {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
          }

          .setting-description {
            font-size: 12px;
            color: #666;
            font-weight: normal;
          }
        }

        .setting-control {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }
    }

    .settings-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 32px;
      padding-top: 24px;
      border-top: 1px solid #f0f0f0;
    }
  }
}

// 深色主题
[data-theme="dark"] {
  .settings-view {
    .settings-header {
      h2 {
        color: #fff;
      }

      p {
        color: #bfbfbf;
      }
    }

    .settings-content {
      .settings-section {
        h3 {
          color: #fff;
        }

        .setting-item {
          background: #2d2d2d;

          .setting-label {
            span {
              color: #fff;
            }

            .setting-description {
              color: #bfbfbf;
            }
          }
        }
      }

      .settings-actions {
        border-color: #303030;
      }
    }
  }
}
</style>