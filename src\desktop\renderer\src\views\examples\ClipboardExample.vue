<template>
  <div class="clipboard-example">
    <a-page-header
      title="剪贴板示例"
      subtitle="演示如何读写剪贴板内容"
      @back="() => router.push('/examples')"
    />

    <a-row :gutter="[24, 24]">
      <!-- 文本操作 -->
      <a-col :span="24">
        <a-card title="文本操作">
          <a-form layout="vertical">
            <a-row :gutter="[16, 16]">
              <a-col :span="24">
                <a-form-item label="文本内容">
                  <a-textarea
                    v-model:value="textContent"
                    :rows="4"
                    placeholder="请输入要复制到剪贴板的文本内容"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item>
                  <a-space>
                    <a-button type="primary" @click="copyText">复制文本</a-button>
                    <a-button @click="pasteText">粘贴文本</a-button>
                    <a-button @click="clearClipboard">清空剪贴板</a-button>
                  </a-space>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>
      </a-col>

      <!-- HTML操作 -->
      <a-col :span="24">
        <a-card title="HTML操作">
          <a-form layout="vertical">
            <a-row :gutter="[16, 16]">
              <a-col :span="24">
                <a-form-item label="HTML内容">
                  <a-textarea
                    v-model:value="htmlContent"
                    :rows="4"
                    placeholder="请输入要复制到剪贴板的HTML内容"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item>
                  <a-space>
                    <a-button type="primary" @click="copyHTML">复制HTML</a-button>
                    <a-button @click="pasteHTML">粘贴HTML</a-button>
                  </a-space>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>
      </a-col>

      <!-- 当前剪贴板内容 -->
      <a-col :xs="24" :sm="12">
        <a-card title="当前剪贴板内容">
          <a-descriptions :column="1" bordered>
            <a-descriptions-item label="文本内容">
              <div class="clipboard-content">
                {{ currentText || '暂无内容' }}
              </div>
            </a-descriptions-item>
            <a-descriptions-item label="HTML内容">
              <div class="clipboard-content">
                {{ currentHTML || '暂无内容' }}
              </div>
            </a-descriptions-item>
            <a-descriptions-item label="更新时间">
              {{ lastUpdateTime || '-' }}
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-col>

      <!-- 快速操作 -->
      <a-col :xs="24" :sm="12">
        <a-card title="快速操作">
          <a-space direction="vertical" style="width: 100%">
            <a-button block @click="copyTimestamp">
              复制时间戳
            </a-button>
            <a-button block @click="copyUUID">
              复制UUID
            </a-button>
            <a-button block @click="copyDeviceInfo">
              复制设备信息
            </a-button>
            <a-button block @click="copyCurrentUrl">
              复制当前URL
            </a-button>
          </a-space>
        </a-card>
      </a-col>

      <!-- 操作历史 -->
      <a-col :span="24">
        <a-card title="操作历史">
          <a-timeline>
            <a-timeline-item v-for="log in clipboardHistory" :key="log.id" :color="log.color">
              <template #dot><component :is="log.icon" /></template>
              <div class="log-content">
                <div class="log-message">{{ log.message }}</div>
                <div class="log-time">{{ log.time }}</div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </a-card>
      </a-col>

      <!-- 权限说明 -->
      <a-col :span="24">
        <a-card title="权限说明">
          <a-alert
            message="剪贴板权限"
            description="读取剪贴板需要用户授权。如果遇到权限问题，请在浏览器设置中允许剪贴板访问权限。"
            type="info"
            show-icon
          />
          <div class="permission-info">
            <h4>权限检查：</h4>
            <a-descriptions :column="1" bordered>
              <a-descriptions-item label="读取权限">
                <a-tag :color="clipboardPermissions.read ? 'green' : 'red'">
                  {{ clipboardPermissions.read ? '已授权' : '未授权' }}
                </a-tag>
              </a-descriptions-item>
              <a-descriptions-item label="写入权限">
                <a-tag :color="clipboardPermissions.write ? 'green' : 'red'">
                  {{ clipboardPermissions.write ? '已授权' : '未授权' }}
                </a-tag>
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  CopyOutlined,
  SnippetsOutlined,
  ClearOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  ClockCircleOutlined,
  IdcardOutlined,
  DesktopOutlined,
  LinkOutlined
} from '@ant-design/icons-vue'

// 路由
const router = useRouter()

// 状态
const textContent = ref('')
const htmlContent = ref('')
const currentText = ref('')
const currentHTML = ref('')
const lastUpdateTime = ref('')

// 剪贴板权限
const clipboardPermissions = reactive({
  read: false,
  write: false
})

// 操作历史
const clipboardHistory = ref<Array<{
  id: number
  message: string
  time: string
  color: string
  icon: any
}>>([])

// 获取剪贴板服务
const getClipboardService = async () => {
  try {
    const { ServiceFactory } = await import('core/src/services')
    const serviceFactory = ServiceFactory.getInstance()
    return await serviceFactory.getServiceManager().getClipboard()
  } catch (error) {
    console.error('获取剪贴板服务失败:', error)
    message.error('获取剪贴板服务失败')
    return null
  }
}

// 复制文本
const copyText = async () => {
  if (!textContent.value) {
    message.warning('请输入要复制的文本内容')
    return
  }

  try {
    const clipboardService = await getClipboardService()
    if (!clipboardService) return

    await clipboardService.writeText(textContent.value)
    message.success('文本复制成功')
    addHistory(`复制文本: ${textContent.value.substring(0, 50)}...`, 'success')
  } catch (error) {
    message.error('文本复制失败')
    addHistory('文本复制失败', 'error')
  }
}

// 粘贴文本
const pasteText = async () => {
  try {
    const clipboardService = await getClipboardService()
    if (!clipboardService) return

    const text = await clipboardService.readText()
    currentText.value = text
    textContent.value = text
    lastUpdateTime.value = new Date().toLocaleString()

    message.success('文本粘贴成功')
    addHistory(`粘贴文本: ${text.substring(0, 50)}...`, 'success')
  } catch (error) {
    message.error('文本粘贴失败')
    addHistory('文本粘贴失败', 'error')
  }
}

// 复制HTML
const copyHTML = async () => {
  if (!htmlContent.value) {
    message.warning('请输入要复制的HTML内容')
    return
  }

  try {
    const clipboardService = await getClipboardService()
    if (!clipboardService) return

    await clipboardService.writeHTML(htmlContent.value)
    message.success('HTML复制成功')
    addHistory(`复制HTML: ${htmlContent.value.substring(0, 50)}...`, 'success')
  } catch (error) {
    message.error('HTML复制失败')
    addHistory('HTML复制失败', 'error')
  }
}

// 粘贴HTML
const pasteHTML = async () => {
  try {
    const clipboardService = await getClipboardService()
    if (!clipboardService) return

    const html = await clipboardService.readHTML()
    currentHTML.value = html
    htmlContent.value = html
    lastUpdateTime.value = new Date().toLocaleString()

    message.success('HTML粘贴成功')
    addHistory(`粘贴HTML: ${html.substring(0, 50)}...`, 'success')
  } catch (error) {
    message.error('HTML粘贴失败')
    addHistory('HTML粘贴失败', 'error')
  }
}

// 清空剪贴板
const clearClipboard = async () => {
  try {
    const clipboardService = await getClipboardService()
    if (!clipboardService) return

    await clipboardService.clear()
    currentText.value = ''
    currentHTML.value = ''
    lastUpdateTime.value = new Date().toLocaleString()

    message.success('剪贴板已清空')
    addHistory('清空剪贴板', 'success')
  } catch (error) {
    message.error('清空剪贴板失败')
    addHistory('清空剪贴板失败', 'error')
  }
}

// 复制时间戳
const copyTimestamp = async () => {
  const timestamp = Date.now().toString()
  textContent.value = timestamp
  await copyText()
}

// 复制UUID
const copyUUID = async () => {
  const uuid = generateUUID()
  textContent.value = uuid
  await copyText()
}

// 复制设备信息
const copyDeviceInfo = async () => {
  const deviceInfo = {
    userAgent: navigator.userAgent,
    platform: navigator.platform,
    language: navigator.language,
    screenSize: `${window.screen.width}x${window.screen.height}`,
    timestamp: new Date().toISOString()
  }
  textContent.value = JSON.stringify(deviceInfo, null, 2)
  await copyText()
}

// 复制当前URL
const copyCurrentUrl = async () => {
  textContent.value = window.location.href
  await copyText()
}

// 生成UUID
const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0
    const v = c === 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

// 添加历史记录
const addHistory = (message: string, type: 'success' | 'error' | 'info') => {
  const typeMap = {
    success: { color: 'green', icon: CheckCircleOutlined },
    error: { color: 'red', icon: ExclamationCircleOutlined },
    info: { color: 'blue', icon: InfoCircleOutlined }
  }

  clipboardHistory.value.unshift({
    id: Date.now(),
    message,
    time: new Date().toLocaleString(),
    color: typeMap[type].color,
    icon: typeMap[type].icon
  })

  // 最多保留50条记录
  if (clipboardHistory.value.length > 50) {
    clipboardHistory.value = clipboardHistory.value.slice(0, 50)
  }
}

// 检查剪贴板权限
const checkClipboardPermissions = async () => {
  try {
    const clipboardService = await getClipboardService()
    if (!clipboardService) return

    // 尝试读取剪贴板来检查权限
    try {
      await clipboardService.readText()
      clipboardPermissions.read = true
    } catch (error) {
      clipboardPermissions.read = false
    }

    // 尝试写入剪贴板来检查权限
    try {
      await clipboardService.writeText('test')
      clipboardPermissions.write = true
    } catch (error) {
      clipboardPermissions.write = false
    }
  } catch (error) {
    console.error('检查剪贴板权限失败:', error)
  }
}

// 初始化
onMounted(async () => {
  await checkClipboardPermissions()
  addHistory('剪贴板示例页面已加载', 'info')
})
</script>

<style lang="less" scoped>
.clipboard-example {
  max-width: 1200px;
  margin: 0 auto;
}

.clipboard-content {
  max-height: 100px;
  overflow-y: auto;
  word-break: break-all;
  font-family: monospace;
  font-size: 12px;
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
}

.permission-info {
  margin-top: 16px;

  h4 {
    margin-bottom: 8px;
    color: #1890ff;
  }
}

.log-content {
  .log-message {
    font-weight: 500;
    margin-bottom: 4px;
  }

  .log-time {
    font-size: 12px;
    color: #666;
  }
}
</style>