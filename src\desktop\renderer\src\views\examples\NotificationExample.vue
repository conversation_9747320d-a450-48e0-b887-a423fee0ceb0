<template>
  <div class="notification-example">
    <a-page-header
      title="通知服务示例"
      subtitle="演示如何发送和处理系统通知"
      @back="() => router.push('/examples')"
    />

    <a-row :gutter="[24, 24]">
      <!-- 通知设置 -->
      <a-col :span="24">
        <a-card title="通知设置">
          <a-form layout="vertical">
            <a-row :gutter="[16, 16]">
              <a-col :xs="24" :sm="12">
                <a-form-item label="通知标题">
                  <a-input v-model:value="notificationOptions.title" placeholder="请输入通知标题" />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12">
                <a-form-item label="通知内容">
                  <a-input v-model:value="notificationOptions.body" placeholder="请输入通知内容" />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="8">
                <a-form-item label="图标URL">
                  <a-input v-model:value="notificationOptions.icon" placeholder="图标URL" />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="8">
                <a-form-item label="图片URL">
                  <a-input v-model:value="notificationOptions.image" placeholder="图片URL" />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="8">
                <a-form-item label="通知标签">
                  <a-input v-model:value="notificationOptions.tag" placeholder="通知标签" />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="8">
                <a-form-item label="静音模式">
                  <a-switch v-model:checked="notificationOptions.silent" />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="8">
                <a-form-item label="需要交互">
                  <a-switch v-model:checked="notificationOptions.requireInteraction" />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="8">
                <a-form-item label="重新通知">
                  <a-switch v-model:checked="notificationOptions.renotify" />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item>
                  <a-space>
                    <a-button type="primary" @click="sendNotification">发送通知</a-button>
                    <a-button @click="requestPermission">请求权限</a-button>
                    <a-button @click="checkPermission">检查权限</a-button>
                    <a-button danger @click="closeAllNotifications">关闭所有</a-button>
                  </a-space>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>
      </a-col>

      <!-- 权限状态 -->
      <a-col :xs="24" :sm="12">
        <a-card title="权限状态">
          <a-descriptions :column="1" bordered>
            <a-descriptions-item label="通知权限">
              <a-tag :color="permissionStatus.color">
                {{ permissionStatus.text }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="支持通知">
              <a-tag :color="isSupported ? 'green' : 'red'">
                {{ isSupported ? '支持' : '不支持' }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="活动通知数量">
              {{ activeNotifications.length }}
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-col>

      <!-- 快速模板 -->
      <a-col :xs="24" :sm="12">
        <a-card title="快速模板">
          <a-space direction="vertical" style="width: 100%">
            <a-button block @click="useTemplate('success')">
              <template #icon><check-circle-outlined /></template>
              成功通知
            </a-button>
            <a-button block @click="useTemplate('warning')">
              <template #icon><warning-outlined /></template>
              警告通知
            </a-button>
            <a-button block @click="useTemplate('error')">
              <template #icon><close-circle-outlined /></template>
              错误通知
            </a-button>
            <a-button block @click="useTemplate('info')">
              <template #icon><info-circle-outlined /></template>
              信息通知
            </a-button>
          </a-space>
        </a-card>
      </a-col>

      <!-- 活动通知 -->
      <a-col :span="24">
        <a-card title="活动通知">
          <a-empty v-if="activeNotifications.length === 0" description="暂无活动通知" />
          <a-list v-else :data-source="activeNotifications" size="small">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta
                  :title="item.title"
                  :description="item.body"
                >
                  <template #avatar>
                    <a-avatar :style="{ backgroundColor: item.color }">
                      <component :is="item.icon" />
                    </a-avatar>
                  </template>
                </a-list-item-meta>
                <template #extra>
                  <a-button type="link" size="small" @click="closeNotification(item.id)">
                    关闭
                  </a-button>
                </template>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>

      <!-- 通知历史 -->
      <a-col :span="24">
        <a-card title="通知历史">
          <a-timeline>
            <a-timeline-item v-for="log in notificationHistory" :key="log.id" :color="log.color">
              <template #dot><component :is="log.icon" /></template>
              <div class="log-content">
                <div class="log-message">{{ log.message }}</div>
                <div class="log-time">{{ log.time }}</div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  CheckCircleOutlined,
  WarningOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
  BellOutlined,
  StopOutlined
} from '@ant-design/icons-vue'

// 路由
const router = useRouter()

// 通知选项
const notificationOptions = reactive({
  title: '测试通知',
  body: '这是一个测试通知',
  icon: '',
  image: '',
  tag: '',
  silent: false,
  requireInteraction: false,
  renotify: false
})

// 权限状态
const permissionStatus = ref({
  text: '未知',
  color: 'default'
})

// 活动通知
const activeNotifications = ref<Array<{
  id: string
  title: string
  body: string
  icon: any
  color: string
}>>([])

// 通知历史
const notificationHistory = ref<Array<{
  id: number
  message: string
  time: string
  color: string
  icon: any
}>>([])

// 是否支持通知
const isSupported = computed(() => 'Notification' in window)

// 获取通知服务
const getNotificationService = async () => {
  try {
    const { ServiceFactory } = await import('core/src/services')
    const serviceFactory = ServiceFactory.getInstance()
    return await serviceFactory.getServiceManager().getNotification()
  } catch (error) {
    console.error('获取通知服务失败:', error)
    message.error('获取通知服务失败')
    return null
  }
}

// 请求权限
const requestPermission = async () => {
  if (!isSupported.value) {
    message.error('此浏览器不支持通知')
    return
  }

  try {
    const permission = await Notification.requestPermission()
    updatePermissionStatus(permission)
    message.success(`权限请求成功: ${permission}`)
    addHistory(`请求通知权限: ${permission}`, 'info')
  } catch (error) {
    message.error('权限请求失败')
    addHistory('权限请求失败', 'error')
  }
}

// 检查权限
const checkPermission = async () => {
  if (!isSupported.value) {
    message.error('此浏览器不支持通知')
    return
  }

  const permission = Notification.permission
  updatePermissionStatus(permission)
  message.info(`当前权限: ${permission}`)
}

// 更新权限状态
const updatePermissionStatus = (permission: string) => {
  const statusMap = {
    granted: { text: '已授权', color: 'green' },
    denied: { text: '已拒绝', color: 'red' },
    default: { text: '默认', color: 'orange' }
  }

  permissionStatus.value = statusMap[permission] || { text: '未知', color: 'default' }
}

// 发送通知
const sendNotification = async () => {
  if (!isSupported.value) {
    message.error('此浏览器不支持通知')
    return
  }

  if (Notification.permission !== 'granted') {
    message.warning('请先请求通知权限')
    return
  }

  try {
    const notificationService = await getNotificationService()
    if (!notificationService) return

    await notificationService.show(notificationOptions.title, notificationOptions)

    // 添加到活动通知列表
    const id = `notification_${Date.now()}`
    activeNotifications.value.push({
      id,
      title: notificationOptions.title,
      body: notificationOptions.body,
      icon: BellOutlined,
      color: 'blue'
    })

    message.success('通知发送成功')
    addHistory(`发送通知: ${notificationOptions.title}`, 'success')

    // 5秒后自动关闭
    setTimeout(() => {
      closeNotification(id)
    }, 5000)
  } catch (error) {
    message.error('通知发送失败')
    addHistory('通知发送失败', 'error')
  }
}

// 关闭通知
const closeNotification = async (id: string) => {
  try {
    const notificationService = await getNotificationService()
    if (!notificationService) return

    await notificationService.close(id)
    activeNotifications.value = activeNotifications.value.filter(n => n.id !== id)
    addHistory(`关闭通知: ${id}`, 'info')
  } catch (error) {
    message.error('关闭通知失败')
    addHistory('关闭通知失败', 'error')
  }
}

// 关闭所有通知
const closeAllNotifications = async () => {
  try {
    const notificationService = await getNotificationService()
    if (!notificationService) return

    await notificationService.closeAll()
    activeNotifications.value = []
    message.success('所有通知已关闭')
    addHistory('关闭所有通知', 'success')
  } catch (error) {
    message.error('关闭所有通知失败')
    addHistory('关闭所有通知失败', 'error')
  }
}

// 使用模板
const useTemplate = (type: string) => {
  const templates = {
    success: {
      title: '操作成功',
      body: '您的操作已成功完成',
      icon: CheckCircleOutlined,
      color: 'green'
    },
    warning: {
      title: '警告',
      body: '请注意，有一些潜在的问题',
      icon: WarningOutlined,
      color: 'orange'
    },
    error: {
      title: '操作失败',
      body: '抱歉，操作失败了，请重试',
      icon: CloseCircleOutlined,
      color: 'red'
    },
    info: {
      title: '信息提示',
      body: '这是一条信息提示',
      icon: InfoCircleOutlined,
      color: 'blue'
    }
  }

  const template = templates[type]
  if (template) {
    Object.assign(notificationOptions, {
      title: template.title,
      body: template.body
    })
  }
}

// 添加历史记录
const addHistory = (message: string, type: 'success' | 'error' | 'info' | 'warning') => {
  const typeMap = {
    success: { color: 'green', icon: CheckCircleOutlined },
    error: { color: 'red', icon: CloseCircleOutlined },
    info: { color: 'blue', icon: InfoCircleOutlined },
    warning: { color: 'orange', icon: WarningOutlined }
  }

  notificationHistory.value.unshift({
    id: Date.now(),
    message,
    time: new Date().toLocaleString(),
    color: typeMap[type].color,
    icon: typeMap[type].icon
  })

  // 最多保留50条记录
  if (notificationHistory.value.length > 50) {
    notificationHistory.value = notificationHistory.value.slice(0, 50)
  }
}

// 初始化
onMounted(() => {
  if (isSupported.value) {
    updatePermissionStatus(Notification.permission)
    addHistory('通知服务示例页面已加载', 'info')
  } else {
    addHistory('浏览器不支持通知', 'error')
  }
})
</script>

<style lang="less" scoped>
.notification-example {
  max-width: 1200px;
  margin: 0 auto;
}

.log-content {
  .log-message {
    font-weight: 500;
    margin-bottom: 4px;
  }

  .log-time {
    font-size: 12px;
    color: #666;
  }
}
</style>