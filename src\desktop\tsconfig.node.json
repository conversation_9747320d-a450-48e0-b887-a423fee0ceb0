{"extends": "../../tsconfig.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./", "composite": true, "declaration": true, "declarationMap": true, "sourceMap": true, "module": "CommonJS", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true}, "include": ["main/**/*", "preload/**/*", "build.ts", "vite.config.ts"], "exclude": ["node_modules", "dist", "renderer"]}