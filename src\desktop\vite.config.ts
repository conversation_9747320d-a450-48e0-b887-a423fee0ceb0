/**
 * Desktop端Vite配置
 */

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig(({ mode }) => {
  const isDevelopment = mode === 'development'
  const isProduction = mode === 'production'

  return {
    // 项目根目录
    root: 'renderer',

    // 基础配置
    base: './',
    mode: mode,

    // 插件配置
    plugins: [
      vue()
    ],

    // 路径解析配置
    resolve: {
      alias: {
        '@': resolve(__dirname, 'renderer/src'),
        '@common': resolve(__dirname, '../common/src'),
        '@core': resolve(__dirname, '../core/src')
      }
    },

    // 构建配置
    build: {
      // 目标环境
      target: 'es2015',

      // 构建输出目录
      outDir: resolve(__dirname, '../../dist/desktop/renderer'),

      // 静态资源目录
      assetsDir: 'assets',

      // 是否生成 sourcemap
      sourcemap: isDevelopment,

      // 压缩配置
      minify: isProduction ? 'terser' : false,

      // 清空输出目录
      emptyOutDir: true,

      // 代码拆分配置
      rollupOptions: {
        input: resolve(__dirname, 'renderer/index.html'),
        output: {
          // 代码分割配置
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',

          // 手动分包
          manualChunks: {
            // Vue相关库
            'vue-vendor': ['vue', 'vue-router', 'pinia'],
            // UI组件库
            'ant-design-vue': ['ant-design-vue', '@ant-design/icons-vue'],
            // Electron相关
            'electron-vendor': ['@electron-toolkit/preload', '@electron-toolkit/utils']
          }
        }
      },

      // 压缩选项
      terserOptions: {
        compress: {
          drop_console: isProduction,
          drop_debugger: isProduction
        }
      }
    },

    // 开发服务器配置
    server: {
      host: '0.0.0.0',
      port: 5173,
      open: false, // Electron会打开窗口
      cors: true,
      strictPort: true
    },

    // 预览服务器配置
    preview: {
      host: '0.0.0.0',
      port: 4173,
      open: false,
      cors: true
    },

    // CSS配置
    css: {
      // CSS模块配置
      modules: {
        localsConvention: 'camelCase'
      },

      // CSS预处理器配置
      preprocessorOptions: {
        less: {
          // 全局变量
          additionalData: `@import "@/assets/styles/variables.less";`,
          // 其他Less配置
          javascriptEnabled: true,
          modifyVars: {
            // Ant Design Vue 主题变量
            '@primary-color': '#1890ff',
            '@link-color': '#1890ff',
            '@success-color': '#52c41a',
            '@warning-color': '#faad14',
            '@error-color': '#f5222d',
            '@font-size-base': '14px',
            '@heading-color': 'rgba(0, 0, 0, 0.85)',
            '@text-color': 'rgba(0, 0, 0, 0.65)',
            '@text-color-secondary': 'rgba(0, 0, 0, 0.45)',
            '@disabled-color': 'rgba(0, 0, 0, 0.25)',
            '@border-radius-base': '4px',
            '@border-color-base': '#d9d9d9',
            '@box-shadow-base': '0 2px 8px rgba(0, 0, 0, 0.15)'
          }
        }
      }
    },

    // 依赖优化选项
    optimizeDeps: {
      include: [
        'vue',
        'vue-router',
        'pinia',
        'ant-design-vue',
        '@ant-design/icons-vue',
        '@electron-toolkit/preload',
        '@electron-toolkit/utils'
      ],
      exclude: ['electron']
    },

    // 定义全局变量
    define: {
      __VUE_OPTIONS_API__: true,
      __VUE_PROD_DEVTOOLS__: false,
      __DESKTOP__: true,
      __WEB__: false,
      __PLATFORM__: JSON.stringify('desktop'),
      __ENVIRONMENT__: JSON.stringify(mode)
    },

    // JSON配置
    json: {
      namedExports: true,
      stringify: true
    },

    // ESBuild配置
    esbuild: {
      // 是否启用JSX转换
      jsxFactory: 'h',
      jsxFragment: 'Fragment',

      // 是否去除日志
      drop: isProduction ? ['console', 'debugger'] : []
    },

    // 静态资源处理
    assetsInclude: ['**/*.png', '**/*.jpg', '**/*.jpeg', '**/*.gif', '**/*.svg', '**/*.webp'],

    // 日志级别
    logLevel: isDevelopment ? 'info' : 'warn',

    // 环境变量前缀
    envPrefix: 'VITE_DESKTOP_'
  }
})
