# Team AI 2.0 构建指南

## 快速开始

### 开发环境

```bash
# Web 端开发
pnpm dev:web

# Desktop 端开发
pnpm dev:desktop
```

### 生产构建

```bash
# 构建 Web 端
pnpm build:web

# 构建 Desktop 端（源代码）
pnpm build:desktop

# 构建 Desktop 端 installer 安装包
pnpm installer
```

## 构建产物

### Web 端构建产物
- 位置：`dist/web/`
- 包含：HTML、CSS、JS 文件
- 用途：部署到 Web 服务器

### Desktop 端构建产物

#### 源代码构建
- 位置：`dist/desktop/`
- 包含：
  - `main/index.js` - 主进程
  - `preload/index.js` - 预加载脚本
  - `renderer/` - 渲染进程（HTML、CSS、JS）

#### EXE 安装包
- 位置：`dist/desktop/`
- 包含：
  - `Team AI 2.0-1.0.0.exe` - 通用安装程序（x64 + ia32）
  - `Team AI 2.0-1.0.0-x64.exe` - 64位专用安装程序
  - `Team AI 2.0-1.0.0-ia32.exe` - 32位专用安装程序
  - `win-unpacked/` - 未打包的 64位 应用程序
  - `win-ia32-unpacked/` - 未打包的 32位 应用程序
  - `main/index.js` - 主进程源代码
  - `preload/index.js` - 预加载脚本源代码
  - `renderer/` - 渲染进程源代码

## 命令说明

| 命令 | 说明 |
|------|------|
| `pnpm dev:web` | 启动 Web 端开发服务器 |
| `pnpm dev:desktop` | 启动 Desktop 端开发环境 |
| `pnpm build:web` | 构建 Web 端生产版本 |
| `pnpm build:desktop` | 构建 Desktop 端源代码 |
| `pnpm installer` | 构建 Desktop 端 EXE 安装包 |
| `pnpm dist:desktop` | 同 `pnpm installer` |
| `pnpm dist:desktop:dir` | 构建未打包的 Desktop 应用 |

## 技术栈

- **前端框架**: Vue 3 + TypeScript
- **构建工具**: Vite
- **桌面框架**: Electron
- **UI 组件库**: Ant Design Vue
- **包管理器**: pnpm
- **打包工具**: Electron Builder

## 注意事项

1. **Web 和 Desktop 独立构建**：两个端的构建互不影响
2. **EXE 文件签名**：构建过程会尝试对 EXE 文件进行签名
3. **图标要求**：Desktop 应用图标需要至少 256x256 像素
4. **依赖管理**：使用 pnpm workspace 管理多包依赖

## 故障排除

### 常见问题

1. **构建失败**：检查 Node.js 版本和依赖安装
2. **EXE 无法运行**：检查 Windows Defender 或杀毒软件
3. **开发服务器启动失败**：检查端口占用情况

### 清理缓存

```bash
# 清理所有构建产物
pnpm clean

# 重新安装依赖
pnpm install
```
