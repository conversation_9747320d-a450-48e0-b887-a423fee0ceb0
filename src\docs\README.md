# Team AI 2.0

基于 Vue3 和 Electron 的跨平台应用框架，支持 Web 和 Desktop 双端

## 🚀 特性

- **跨平台**: 一套代码，同时支持 Web 和 Desktop
- **现代化**: 基于 Vue 3 + TypeScript + Vite
- **模块化**: 清晰的分层架构，易于扩展和维护
- **安全**: Electron 安全配置，防止代码注入攻击
- **高性能**: 优化的构建系统和性能
- **响应式**: 适配不同屏幕尺寸
- **主题系统**: 支持浅色/深色主题
- **类型安全**: 完整的 TypeScript 类型定义

## 🏗️ 架构

```
team-ai-2.0/
├── src/
│   ├── common/          # 共享代码层
│   │   ├── src/
│   │   │   ├── types/   # 类型定义
│   │   │   ├── utils/   # 工具函数
│   │   │   └── constants/ # 常量定义
│   │   └── package.json
│   ├── core/            # 核心业务逻辑层
│   │   ├── src/
│   │   │   ├── adapters/ # 适配器模式
│   │   │   ├── services/ # 服务层
│   │   │   └── interfaces/ # 接口定义
│   │   └── package.json
│   ├── web/             # Web端实现
│   │   ├── src/
│   │   │   ├── views/   # 页面组件
│   │   │   ├── components/ # 通用组件
│   │   │   └── assets/  # 静态资源
│   │   └── package.json
│   └── desktop/         # Desktop端实现
│       ├── main/       # Electron主进程
│       ├── preload/    # 预加载脚本
│       ├── renderer/   # 渲染进程
│       └── package.json
├── docs/               # 文档
└── package.json        # 根配置
```

## 🛠️ 技术栈

- **前端框架**: Vue 3 + Composition API
- **构建工具**: Vite
- **类型系统**: TypeScript
- **UI组件库**: Ant Design Vue
- **状态管理**: Pinia
- **路由**: Vue Router
- **桌面端**: Electron
- **包管理**: pnpm + workspaces

## 📦 安装

### 环境要求

- Node.js >= 16.0.0
- pnpm >= 7.0.0

### 安装步骤

```bash
# 克隆项目
git clone https://github.com/team-ai-2.0.git
cd team-ai-2.0

# 安装依赖
pnpm install

# 安装所有工作空间依赖
pnpm install --frozen-lockfile
```

## 🚀 开发

### Web 端开发

```bash
# 启动 Web 开发服务器
pnpm dev:web

# 访问 http://localhost:3000
```

### Desktop 端开发

```bash
# 启动 Desktop 开发服务器
pnpm dev:desktop

# Electron 应用会自动启动
```

## 构建

### Web 端构建

```bash
# 构建 Web 应用
pnpm build:web

# 预览构建结果
pnpm preview:web
```

### Desktop 端构建

```bash
# 构建 Desktop 应用
pnpm build:desktop

# 打包成可执行文件
pnpm dist:installer
```

### 完整构建

```bash
# 构建所有平台
pnpm build

# 打包所有平台
pnpm dist
```

## 📁 项目结构

### 核心模块

#### 1. 共享代码层 (common)
- **类型定义**: 统一的 TypeScript 类型定义
- **工具函数**: 跨平台工具函数库
- **常量定义**: 应用程序常量配置

#### 2. 核心业务逻辑层 (core)
- **适配器模式**: 跨平台适配器实现
- **服务层**: 统一的服务管理和依赖注入
- **接口定义**: 核心业务接口定义

#### 3. Web 端实现 (web)
- **页面组件**: Web 端专属页面和组件
- **路由配置**: Web 端路由配置
- **静态资源**: Web 端静态资源管理

#### 4. Desktop 端实现 (desktop)
- **主进程**: Electron 主进程逻辑
- **预加载脚本**: 安全的 API 桥接
- **渲染进程**: Desktop 端界面实现

## 🔧 配置

### 环境变量

创建 `.env` 文件：

```env
# 应用配置
VITE_APP_TITLE=Team AI 2.0
VITE_APP_VERSION=1.0.0

# API 配置
VITE_API_BASE_URL=http://localhost:8080/api

# 构建配置
VITE_PUBLIC_PATH=/
VITE_BUILD_TARGET=web
```

### TypeScript 配置

项目包含多个 TypeScript 配置文件：

- `tsconfig.json`: 根配置，包含项目引用
- `tsconfig.node.json`: Node.js 环境配置
- `tsconfig.web.json`: Web 端配置
- `tsconfig.desktop.json`: Desktop 端配置

### Vite 配置

- `vite.config.ts`: 通用配置
- `vite.web.config.ts`: Web 端配置
- `vite.desktop.config.ts`: Desktop 端配置
- `electron.vite.config.ts`: Electron 构建配置

## 📚 使用示例

### 基础使用

```typescript
// 导入服务工厂
import { ServiceFactory } from '@team-ai/core'

// 初始化服务
const serviceFactory = ServiceFactory.getInstance()
await serviceFactory.init()

// 使用存储服务
const storage = serviceFactory.getStorage()
await storage.set('user-data', { name: 'wanghu' })
const data = await storage.get('user-data')

// 使用网络服务
const network = serviceFactory.getNetwork()
const response = await network.get('/api/users')
```

### 适配器使用

```typescript
// 导入适配器工厂
import { AdapterFactory } from '@team-ai/core'

// 创建适配器
const adapterFactory = new AdapterFactory()
await adapterFactory.initialize()

// 使用特定适配器
const storageAdapter = adapterFactory.getAdapter('storage')
await storageAdapter.set('key', 'value')
```

### 组件使用

```vue
<template>
  <div>
    <a-button @click="saveData">保存数据</a-button>
    <a-button @click="loadData">加载数据</a-button>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useStorage } from '@team-ai/core'

const storage = useStorage()
const data = ref('')

const saveData = async () => {
  await storage.set('demo-key', data.value)
}

const loadData = async () => {
  data.value = await storage.get('demo-key') || ''
}
</script>
```

## 🎨 主题系统

### 主题切换

```typescript
import { useTheme } from '@team-ai/core'

const theme = useTheme()

// 切换主题
await theme.toggleTheme()

// 设置特定主题
await theme.setTheme('dark')

// 获取当前主题
const currentTheme = theme.getCurrentTheme()
```

### 自定义主题

```css
/* 在 CSS 中使用 CSS 变量 */
:root {
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
}

[data-theme="dark"] {
  --primary-color: #40a9ff;
  --success-color: #73d13d;
  --warning-color: #ffc53d;
  --error-color: #ff4d4f;
}
```

## 🔒 安全配置

### Electron 安全配置

- `nodeIntegration: false`: 禁用 Node.js 集成
- `contextIsolation: true`: 启用上下文隔离
- `enableRemoteModule: false`: 禁用远程模块
- `sandbox: true`: 启用沙箱模式

### 预加载脚本

```typescript
// 安全地暴露 API
contextBridge.exposeInMainWorld('electronAPI', {
  window: {
    minimize: () => ipcRenderer.invoke('window:minimize'),
    maximize: () => ipcRenderer.invoke('window:maximize'),
    close: () => ipcRenderer.invoke('window:close')
  }
})
```

## 📱 响应式设计

框架使用 CSS Grid 和 Flexbox 实现响应式布局：

```css
.container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

@media (max-width: 768px) {
  .container {
    grid-template-columns: 1fr;
  }
}
```

## 发布

### 版本管理

```bash
# 更新版本
pnpm version major  # 主要版本
pnpm version minor  # 次要版本
pnpm version patch  # 补丁版本

```

### Desktop 端发布

```bash

## 贡献

欢迎贡献代码！请遵循以下步骤：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/LoginFeature`)
3. 提交更改 (`git commit -m 'Add Login Feature'`)
4. 推送到分支 (`git push origin feature/LoginFeature`)

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 致谢

- [Vue.js](https://vuejs.org/) - 渐进式 JavaScript 框架
- [Electron](https://www.electronjs.org/) - 跨平台桌面应用开发框架
- [Vite](https://vitejs.dev/) - 下一代前端构建工具
- [Ant Design Vue](https://www.antdv.com/) - Vue UI 组件库
- [TypeScript](https://www.typescriptlang.org/) - TypeScript 超集

## 联系我们

- 项目地址: [https://github.com/team-ai-2.0](https://github.com/team-ai-2.0)
- 问题反馈: [https://github.com/team-ai-2.0/issues](https://github.com/team-ai-2.0/issues)
- 官方网站: [https://team-ai-2.0.com](https://team-ai-2.0.com)