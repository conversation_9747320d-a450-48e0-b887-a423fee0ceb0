{"name": "web", "version": "1.0.0", "description": "Web端应用", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "typecheck": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "format": "prettier --write ."}, "dependencies": {"common": "workspace:*", "core": "workspace:*", "vue": "^3.4.30", "vue-router": "^4.5.0", "pinia": "^2.3.1", "ant-design-vue": "^4.2.6", "@ant-design/icons-vue": "^7.0.1"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.5", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/tsconfig": "^0.5.1", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.26.0", "less": "^4.2.0", "prettier": "^3.3.2", "typescript": "^5.5.2", "vite": "^5.3.1", "vue-tsc": "^2.0.22"}}