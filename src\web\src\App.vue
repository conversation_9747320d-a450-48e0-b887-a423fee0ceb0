<template>
  <div class="app-container" :class="{ 'is-mobile': isMobile }">
    <!-- 顶部导航栏 -->
    <a-layout-header class="app-header" v-if="showHeader">
      <div class="header-left">
        <h1 class="app-title">Team AI 2.0</h1>
      </div>
      <div class="header-right">
        <a-menu mode="horizontal" :selectedKeys="[currentRoute]" class="nav-menu">
          <a-menu-item key="home" @click="navigateTo('/home')">首页</a-menu-item>
          <a-menu-item key="examples" @click="navigateTo('/examples')">示例</a-menu-item>
          <a-menu-item key="about" @click="navigateTo('/about')">关于</a-menu-item>
        </a-menu>

        <!-- 主题切换 -->
        <a-switch
          v-model:checked="isDark"
          :checkedChildren="'🌙'"
          :unCheckedChildren="'☀️'"
          @change="toggleTheme"
          class="theme-switch"
        />
      </div>
    </a-layout-header>

    <!-- 主要内容区域 -->
    <a-layout-content class="app-content">
      <RouterView />
    </a-layout-content>

    <!-- 底部信息 -->
    <a-layout-footer class="app-footer" v-if="showFooter">
      <div class="footer-content">
        <p>Team AI 2.0 - 基于Vue3和Electron的跨平台应用框架</p>
        <p>版本: {{ version }}</p>
      </div>
    </a-layout-footer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { ServiceFactory } from 'core/src/services'

// 路由
const route = useRoute()
const router = useRouter()

// 状态
const isMobile = ref(false)
const isDark = ref(false)

// 计算属性
const currentRoute = computed(() => route.name as string)
const showHeader = computed(() => route.name !== 'fullscreen')
const showFooter = computed(() => route.name !== 'fullscreen')

// 版本信息
const version = '1.0.0'

// 方法
const navigateTo = (path: string) => {
  router.push(path)
}

const toggleTheme = async (checked: boolean) => {
  try {
    const serviceFactory = ServiceFactory.getInstance()
    const themeService = await serviceFactory.getServiceManager().getTheme()

    const theme = checked ? 'dark' : 'light'
    await themeService.setTheme(theme)

    message.success(`已切换到${theme === 'dark' ? '深色' : '浅色'}主题`)
  } catch (error) {
    console.error('主题切换失败:', error)
    message.error('主题切换失败')
  }
}

const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

const initTheme = async () => {
  try {
    const serviceFactory = ServiceFactory.getInstance()
    const themeService = await serviceFactory.getServiceManager().getTheme()
    const theme = await themeService.getTheme()

    if (theme === 'dark') {
      isDark.value = true
      document.documentElement.setAttribute('data-theme', 'dark')
    } else if (theme === 'light') {
      isDark.value = false
      document.documentElement.setAttribute('data-theme', 'light')
    }

    // 监听主题变化
    themeService.onThemeChange((newTheme) => {
      isDark.value = newTheme === 'dark'
      document.documentElement.setAttribute('data-theme', newTheme)
    })
  } catch (error) {
    console.error('主题初始化失败:', error)
  }
}

// 生命周期
onMounted(() => {
  checkMobile()
  initTheme()

  // 监听窗口大小变化
  window.addEventListener('resize', checkMobile)
})
</script>

<style lang="less">
.app-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;

  &.is-mobile {
    .app-header {
      flex-direction: column;
      height: auto;
      padding: 10px;

      .header-left {
        margin-bottom: 10px;
      }

      .header-right {
        width: 100%;
        justify-content: space-between;
      }
    }
  }
}

.app-header {
  background: #fff;
  border-bottom: 1px solid #f0f0f0;
  padding: 0 20px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

  .header-left {
    .app-title {
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #1890ff;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 20px;

    .nav-menu {
      border-bottom: none;

      .ant-menu-item {
        &:hover {
          color: #1890ff;
        }

        &.ant-menu-item-selected {
          color: #1890ff;
        }
      }
    }

    .theme-switch {
      transform: scale(0.8);
    }
  }
}

.app-content {
  flex: 1;
  padding: 20px;
  background: #f5f5f5;
}

.app-footer {
  background: #fff;
  border-top: 1px solid #f0f0f0;
  padding: 20px;
  text-align: center;

  .footer-content {
    p {
      margin: 5px 0;
      color: #666;
      font-size: 14px;
    }
  }
}

// 深色主题
[data-theme="dark"] {
  .app-header {
    background: #1f1f1f;
    border-bottom-color: #303030;

    .app-title {
      color: #40a9ff;
    }
  }

  .app-content {
    background: #141414;
  }

  .app-footer {
    background: #1f1f1f;
    border-top-color: #303030;

    .footer-content {
      p {
        color: #bfbfbf;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-header {
    flex-direction: column;
    height: auto;
    padding: 10px;

    .header-left {
      margin-bottom: 10px;
      width: 100%;

      .app-title {
        font-size: 18px;
      }
    }

    .header-right {
      width: 100%;
      justify-content: space-between;
      flex-wrap: wrap;

      .nav-menu {
        flex: 1;
        min-width: 200px;
      }
    }
  }

  .app-content {
    padding: 10px;
  }
}
</style>