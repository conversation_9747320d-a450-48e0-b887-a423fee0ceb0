/**
 * Web端入口文件
 */

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import Antd from 'ant-design-vue'
import App from './App.vue'
import router from './router'

// 导入样式
import 'ant-design-vue/dist/reset.css'
import './assets/styles/index.less'

// 初始化核心服务
import { ServiceFactory } from 'core/src/services'

async function initApp() {
  try {
    // 初始化服务工厂
    const serviceFactory = ServiceFactory.getInstance()
    await serviceFactory.init()

    // 创建Vue应用
    const app = createApp(App)
    const pinia = createPinia()

    // 使用插件
    app.use(pinia)
    app.use(router)
    app.use(Antd)

    // 挂载应用
    app.mount('#app')

    console.log('Web应用初始化完成')
  } catch (error) {
    console.error('Web应用初始化失败:', error)
  }
}

// 启动应用
initApp()