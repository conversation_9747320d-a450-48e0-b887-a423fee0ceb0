/**
 * 路由配置
 */

import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    name: 'home',
    component: () => import('../views/Home.vue'),
    meta: {
      title: '首页',
      requireAuth: false
    }
  },
  {
    path: '/examples',
    name: 'examples',
    component: () => import('../views/Examples.vue'),
    meta: {
      title: '示例',
      requireAuth: false
    }
  },
  {
    path: '/examples/storage',
    name: 'storage-example',
    component: () => import('../views/examples/StorageExample.vue'),
    meta: {
      title: '存储示例',
      requireAuth: false
    }
  },
  {
    path: '/examples/window',
    name: 'window-example',
    component: () => import('../views/examples/WindowExample.vue'),
    meta: {
      title: '窗口示例',
      requireAuth: false
    }
  },
  {
    path: '/examples/notification',
    name: 'notification-example',
    component: () => import('../views/examples/NotificationExample.vue'),
    meta: {
      title: '通知示例',
      requireAuth: false
    }
  },
  {
    path: '/examples/theme',
    name: 'theme-example',
    component: () => import('../views/examples/ThemeExample.vue'),
    meta: {
      title: '主题示例',
      requireAuth: false
    }
  },
  {
    path: '/examples/network',
    name: 'network-example',
    component: () => import('../views/examples/NetworkExample.vue'),
    meta: {
      title: '网络示例',
      requireAuth: false
    }
  },
  {
    path: '/examples/clipboard',
    name: 'clipboard-example',
    component: () => import('../views/examples/ClipboardExample.vue'),
    meta: {
      title: '剪贴板示例',
      requireAuth: false
    }
  },
  {
    path: '/examples/iframe',
    name: 'iframe-example',
    component: () => import('../views/examples/IframeExample.vue'),
    meta: {
      title: 'Iframe示例',
      requireAuth: false
    }
  },
  {
    path: '/about',
    name: 'about',
    component: () => import('../views/About.vue'),
    meta: {
      title: '关于',
      requireAuth: false
    }
  },
  {
    path: '/fullscreen',
    name: 'fullscreen',
    component: () => import('../views/Fullscreen.vue'),
    meta: {
      title: '全屏',
      requireAuth: false,
      hideHeader: true,
      hideFooter: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('../views/NotFound.vue'),
    meta: {
      title: '页面不存在',
      requireAuth: false
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - Team AI 2.0`
  } else {
    document.title = 'Team AI 2.0'
  }

  // 权限检查（如果需要）
  if (to.meta?.requireAuth) {
    // 这里可以添加登录状态检查
    // const isAuthenticated = checkAuth()
    // if (!isAuthenticated) {
    //   next('/login')
    //   return
    // }
  }

  next()
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
})

export default router