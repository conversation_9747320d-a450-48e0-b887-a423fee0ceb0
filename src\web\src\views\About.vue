<template>
  <div class="about-container">
    <a-page-header
      title="关于我们"
      subtitle="了解Team AI 2.0项目信息"
      @back="() => router.push('/')"
    />

    <a-row :gutter="[24, 24]">
      <!-- 项目信息 -->
      <a-col :xs="24" :lg="12">
        <a-card title="项目信息">
          <a-descriptions :column="1" bordered>
            <a-descriptions-item label="项目名称">Team AI 2.0</a-descriptions-item>
            <a-descriptions-item label="版本">1.0.0</a-descriptions-item>
            <a-descriptions-item label="描述">基于Vue3和Electron的跨平台应用框架</a-descriptions-item>
            <a-descriptions-item label="开发语言">TypeScript</a-descriptions-item>
            <a-descriptions-item label="前端框架">Vue 3</a-descriptions-item>
            <a-descriptions-item label="桌面框架">Electron</a-descriptions-item>
            <a-descriptions-item label="构建工具">Vite</a-descriptions-item>
            <a-descriptions-item label="UI组件库">Ant Design Vue</a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-col>

      <!-- 技术栈 -->
      <a-col :xs="24" :lg="12">
        <a-card title="技术栈">
          <a-list :data-source="techStack" size="small">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta :title="item.name" :description="item.description">
                  <template #avatar>
                    <a-avatar :style="{ backgroundColor: item.color }">
                      {{ item.icon }}
                    </a-avatar>
                  </template>
                </a-list-item-meta>
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>

      <!-- 功能特性 -->
      <a-col :span="24">
        <a-card title="功能特性">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :md="8" v-for="feature in features" :key="feature.id">
              <a-card class="feature-card" :bordered="false">
                <template #title>
                  <span class="feature-icon">{{ feature.icon }}</span>
                  {{ feature.title }}
                </template>
                <p>{{ feature.description }}</p>
              </a-card>
            </a-col>
          </a-row>
        </a-card>
      </a-col>

      <!-- 项目结构 -->
      <a-col :span="24">
        <a-card title="项目结构">
          <a-tree
            :tree-data="projectStructure"
            :default-expanded-keys="['src']"
            show-icon
          >
            <template #icon="{ icon }">
              <component :is="icon" />
            </template>
          </a-tree>
        </a-card>
      </a-col>

      <!-- 开发团队 -->
      <a-col :span="24">
        <a-card title="开发团队">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="12" :md="6" v-for="member in teamMembers" :key="member.id">
              <a-card class="team-card" :bordered="false">
                <template #cover>
                  <div class="team-avatar">
                    <a-avatar :size="64">{{ member.avatar }}</a-avatar>
                  </div>
                </template>
                <a-card-meta :title="member.name" :description="member.role">
                  <template #description>
                    <div class="member-description">
                      <p>{{ member.description }}</p>
                      <div class="member-skills">
                        <a-tag v-for="skill in member.skills" :key="skill" size="small">
                          {{ skill }}
                        </a-tag>
                      </div>
                    </div>
                  </template>
                </a-card-meta>
              </a-card>
            </a-col>
          </a-row>
        </a-card>
      </a-col>

      <!-- 联系方式 -->
      <a-col :span="24">
        <a-card title="联系方式">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="8">
              <a-statistic title="GitHub" :value="githubStars" suffix="⭐">
                <template #prefix><github-outlined /></template>
              </a-statistic>
            </a-col>
            <a-col :xs="24" :sm="8">
              <a-statistic title="Issues" :value="openIssues" suffix="🐛">
                <template #prefix><bug-outlined /></template>
              </a-statistic>
            </a-col>
            <a-col :xs="24" :sm="8">
              <a-statistic title="Contributors" :value="contributors" suffix="👥">
                <template #prefix><team-outlined /></template>
              </a-statistic>
            </a-col>
          </a-row>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  GithubOutlined,
  BugOutlined,
  TeamOutlined,
  FolderOutlined,
  FileOutlined,
  CodeOutlined,
  SettingOutlined
} from '@ant-design/icons-vue'

// 路由
const router = useRouter()

// 状态
const githubStars = ref(128)
const openIssues = ref(3)
const contributors = ref(5)

// 技术栈
const techStack = ref([
  {
    name: 'Vue 3',
    description: '现代化的前端框架',
    icon: 'V',
    color: '#4FC08D'
  },
  {
    name: 'TypeScript',
    description: '类型安全的JavaScript',
    icon: 'T',
    color: '#3178C6'
  },
  {
    name: 'Vite',
    description: '快速的构建工具',
    icon: 'V',
    color: '#646CFF'
  },
  {
    name: 'Electron',
    description: '跨平台桌面应用框架',
    icon: 'E',
    color: '#47848F'
  },
  {
    name: 'Ant Design Vue',
    description: '企业级UI组件库',
    icon: 'A',
    color: '#1890FF'
  },
  {
    name: 'Pinia',
    description: 'Vue状态管理',
    icon: 'P',
    color: '#FFD700'
  }
])

// 功能特性
const features = ref([
  {
    id: 1,
    title: '适配器模式',
    icon: '🎯',
    description: '为不同环境提供统一的接口实现'
  },
  {
    id: 2,
    title: '模块化设计',
    icon: '🔧',
    description: '高度解耦的模块化架构'
  },
  {
    id: 3,
    title: '跨平台支持',
    icon: '📱',
    description: '同时支持Web和Desktop双端部署'
  },
  {
    id: 4,
    title: '类型安全',
    icon: '🔒',
    description: '完整的TypeScript类型定义'
  },
  {
    id: 5,
    title: '现代工具链',
    icon: '⚡',
    description: '基于Vite的快速开发体验'
  },
  {
    id: 6,
    title: '企业级UI',
    icon: '🎨',
    description: '基于Ant Design的美观界面'
  }
])

// 项目结构
const projectStructure = ref([
  {
    title: 'src',
    key: 'src',
    icon: FolderOutlined,
    children: [
      {
        title: 'common',
        key: 'common',
        icon: FolderOutlined,
        children: [
          { title: 'types', key: 'types', icon: FileOutlined },
          { title: 'utils', key: 'utils', icon: FileOutlined },
          { title: 'constants', key: 'constants', icon: FileOutlined }
        ]
      },
      {
        title: 'core',
        key: 'core',
        icon: FolderOutlined,
        children: [
          { title: 'interfaces', key: 'interfaces', icon: FileOutlined },
          { title: 'adapters', key: 'adapters', icon: FileOutlined },
          { title: 'services', key: 'services', icon: FileOutlined }
        ]
      },
      {
        title: 'web',
        key: 'web',
        icon: FolderOutlined,
        children: [
          { title: 'src', key: 'web-src', icon: FolderOutlined },
          { title: 'index.html', key: 'index.html', icon: FileOutlined }
        ]
      },
      {
        title: 'desktop',
        key: 'desktop',
        icon: FolderOutlined,
        children: [
          { title: 'main', key: 'main', icon: FolderOutlined },
          { title: 'preload', key: 'preload', icon: FolderOutlined },
          { title: 'renderer', key: 'renderer', icon: FolderOutlined }
        ]
      }
    ]
  }
])

// 团队成员
const teamMembers = ref([
  {
    id: 1,
    name: '张三',
    role: '项目负责人',
    avatar: '👨‍💻',
    description: '负责项目整体架构设计和开发规划',
    skills: ['Vue3', 'TypeScript', '架构设计']
  },
  {
    id: 2,
    name: '李四',
    role: '前端开发',
    avatar: '👩‍💻',
    description: '负责前端界面开发和用户体验优化',
    skills: ['Vue3', 'UI/UX', '前端工程']
  },
  {
    id: 3,
    name: '王五',
    role: '后端开发',
    avatar: '👨‍💼',
    description: '负责后端接口设计和数据库开发',
    skills: ['Node.js', '数据库', 'API设计']
  },
  {
    id: 4,
    name: '赵六',
    role: '测试工程师',
    avatar: '👩‍🔬',
    description: '负责项目测试和质量保证',
    skills: ['测试', '质量保证', '自动化']
  }
])
</script>

<style lang="less" scoped>
.about-container {
  max-width: 1200px;
  margin: 0 auto;
}

.feature-card {
  height: 100%;
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-5px);
  }

  .feature-icon {
    margin-right: 8px;
  }
}

.team-card {
  text-align: center;

  .team-avatar {
    padding: 20px 0;
  }

  .member-description {
    text-align: left;

    p {
      margin-bottom: 8px;
      color: #666;
    }

    .member-skills {
      display: flex;
      flex-wrap: wrap;
      gap: 4px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .team-card {
    .member-description {
      text-align: center;

      .member-skills {
        justify-content: center;
      }
    }
  }
}
</style>