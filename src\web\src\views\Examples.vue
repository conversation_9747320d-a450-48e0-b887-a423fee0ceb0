<template>
  <div class="examples-container">
    <a-page-header
      title="示例页面"
      subtitle="了解框架的各种功能和使用方法"
      @back="() => router.push('/')"
    >
      <template #extra>
        <a-button type="primary" @click="refreshExamples">
          <template #icon><redo-outlined /></template>
          刷新
        </a-button>
      </template>
    </a-page-header>

    <a-row :gutter="[24, 24]">
      <a-col
        v-for="example in examples"
        :key="example.id"
        :xs="24"
        :sm="12"
        :md="8"
        :lg="6"
      >
        <a-card
          :title="example.title"
          :bordered="false"
          hoverable
          @click="navigateToExample(example.path)"
        >
          <template #cover>
            <div class="example-cover">
              <span class="example-icon">{{ example.icon }}</span>
            </div>
          </template>
          <a-card-meta>
            <template #description>
              <p class="example-description">{{ example.description }}</p>
              <div class="example-tags">
                <a-tag
                  v-for="tag in example.tags"
                  :key="tag"
                  :color="getTagColor(tag)"
                  size="small"
                >
                  {{ tag }}
                </a-tag>
              </div>
            </template>
          </a-card-meta>
        </a-card>
      </a-col>
    </a-row>

    <!-- 使用说明 -->
    <a-card title="使用说明" class="instructions-card">
      <a-timeline>
        <a-timeline-item>
          <template #dot><clock-circle-outlined style="font-size: 16px;" /></template>
          <h4>选择示例</h4>
          <p>点击上方的示例卡片，查看具体的实现代码和使用方法。</p>
        </a-timeline-item>
        <a-timeline-item>
          <template #dot><code-outlined style="font-size: 16px;" /></template>
          <h4>查看代码</h4>
          <p>每个示例都包含完整的代码实现，可以直接复制使用。</p>
        </a-timeline-item>
        <a-timeline-item>
          <template #dot><api-outlined style="font-size: 16px;" /></template>
          <h4>API参考</h4>
          <p>参考核心接口定义，了解各个服务的使用方法。</p>
        </a-timeline-item>
        <a-timeline-item>
          <template #dot><rocket-outlined style="font-size: 16px;" /></template>
          <h4>开始开发</h4>
          <p>基于示例代码，开始你的应用开发。</p>
        </a-timeline-item>
      </a-timeline>
    </a-card>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import {
  RedoOutlined,
  ClockCircleOutlined,
  CodeOutlined,
  ApiOutlined,
  RocketOutlined
} from '@ant-design/icons-vue'

// 路由
const router = useRouter()

// 示例列表
const examples = ref([
  {
    id: 1,
    title: '存储服务',
    icon: '💾',
    description: '演示如何使用存储服务进行数据的保存和读取',
    path: '/examples/storage',
    tags: ['基础', '存储', '适配器']
  },
  {
    id: 2,
    title: '窗口管理',
    icon: '🪟',
    description: '演示如何创建和管理应用窗口',
    path: '/examples/window',
    tags: ['窗口', '管理', 'Desktop']
  },
  {
    id: 3,
    title: '通知服务',
    icon: '🔔',
    description: '演示如何发送和处理系统通知',
    path: '/examples/notification',
    tags: ['通知', '交互', '跨平台']
  },
  {
    id: 4,
    title: '主题系统',
    icon: '🎨',
    description: '演示如何实现主题切换功能',
    path: '/examples/theme',
    tags: ['主题', '样式', '用户体验']
  },
  {
    id: 5,
    title: '网络请求',
    icon: '🌐',
    description: '演示如何进行网络请求和数据获取',
    path: '/examples/network',
    tags: ['网络', 'HTTP', 'API']
  },
  {
    id: 6,
    title: '剪贴板',
    icon: '📋',
    description: '演示如何读写剪贴板内容',
    path: '/examples/clipboard',
    tags: ['剪贴板', '交互', '数据']
  },
  {
    id: 7,
    title: 'Iframe嵌入',
    icon: '🔗',
    description: '演示如何在iframe中嵌入应用',
    path: '/examples/iframe',
    tags: ['iframe', '嵌入', '跨域']
  }
])

// 方法
const navigateToExample = (path: string) => {
  router.push(path)
}

const refreshExamples = () => {
  // 刷新示例列表
  console.log('刷新示例列表')
}

const getTagColor = (tag: string) => {
  const colors: Record<string, string> = {
    '基础': 'blue',
    '存储': 'green',
    '适配器': 'purple',
    '窗口': 'orange',
    '管理': 'cyan',
    'Desktop': 'red',
    '通知': 'pink',
    '交互': 'gold',
    '跨平台': 'lime',
    '主题': 'magenta',
    '样式': 'volcano',
    '用户体验': 'geekblue',
    '网络': 'purple',
    'HTTP': 'red',
    'API': 'orange',
    '剪贴板': 'green',
    '数据': 'blue',
    'iframe': 'cyan',
    '嵌入': 'gold',
    '跨域': 'red'
  }
  return colors[tag] || 'default'
}
</script>

<style lang="less" scoped>
.examples-container {
  max-width: 1200px;
  margin: 0 auto;
}

.example-cover {
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  .example-icon {
    font-size: 48px;
  }
}

.example-description {
  color: #666;
  margin-bottom: 12px;
  line-height: 1.5;
}

.example-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.instructions-card {
  margin-top: 24px;

  h4 {
    color: #1890ff;
    margin-bottom: 8px;
  }

  p {
    color: #666;
    margin-bottom: 0;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .example-cover {
    height: 80px;

    .example-icon {
      font-size: 36px;
    }
  }
}
</style>