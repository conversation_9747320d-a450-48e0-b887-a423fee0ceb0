<template>
  <div class="fullscreen-container">
    <div class="fullscreen-content">
      <h1 class="fullscreen-title">全屏模式</h1>
      <p class="fullscreen-description">
        这是一个全屏模式的示例页面，适用于嵌入式场景
      </p>
      <div class="fullscreen-actions">
        <a-button type="primary" @click="exitFullscreen">退出全屏</a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'

const router = useRouter()

const exitFullscreen = () => {
  router.push('/')
}
</script>

<style lang="less" scoped>
.fullscreen-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.fullscreen-content {
  text-align: center;
  max-width: 600px;
  padding: 40px;

  .fullscreen-title {
    font-size: 48px;
    font-weight: 600;
    margin-bottom: 20px;
  }

  .fullscreen-description {
    font-size: 18px;
    margin-bottom: 40px;
    opacity: 0.9;
  }

  .fullscreen-actions {
    display: flex;
    gap: 16px;
    justify-content: center;
  }
}
</style>