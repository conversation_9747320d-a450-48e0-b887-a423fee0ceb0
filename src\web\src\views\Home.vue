<template>
  <div class="home-container">
    <a-row :gutter="[24, 24]">
      <!-- 欢迎卡片 -->
      <a-col :span="24">
        <a-card class="welcome-card">
          <a-row :gutter="[24, 0]" align="middle">
            <a-col :span="8">
              <div class="welcome-icon">
                <span class="icon">🚀</span>
              </div>
            </a-col>
            <a-col :span="16">
              <h1 class="welcome-title">欢迎使用 Team AI 2.0</h1>
              <p class="welcome-description">
                基于Vue3和Electron的跨平台应用框架，支持Web和Desktop双端部署
              </p>
              <div class="welcome-actions">
                <a-button type="primary" size="large" @click="navigateToExamples">
                  查看示例
                </a-button>
                <a-button size="large" @click="navigateToAbout">
                  了解更多
                </a-button>
              </div>
            </a-col>
          </a-row>
        </a-card>
      </a-col>

      <!-- 特性介绍 -->
      <a-col :xs="24" :sm="12" :md="8">
        <a-card class="feature-card" hoverable>
          <template #title>
            <span class="feature-icon">🎯</span>
            <span>适配器模式</span>
          </template>
          <p>
            通过适配器模式，为不同环境（Web/Desktop）提供统一的接口实现，实现代码复用。
          </p>
        </a-card>
      </a-col>

      <a-col :xs="24" :sm="12" :md="8">
        <a-card class="feature-card" hoverable>
          <template #title>
            <span class="feature-icon">🔧</span>
            <span>模块解耦</span>
          </template>
          <p>
            采用模块化设计，各功能模块高度解耦，便于团队协作和维护。
          </p>
        </a-card>
      </a-col>

      <a-col :xs="24" :sm="12" :md="8">
        <a-card class="feature-card" hoverable>
          <template #title>
            <span class="feature-icon">📱</span>
            <span>跨平台</span>
          </template>
          <p>
            一套代码，同时支持Web和Desktop双端部署，满足不同场景需求。
          </p>
        </a-card>
      </a-col>

      <!-- 快速开始 -->
      <a-col :span="24">
        <a-card class="quickstart-card">
          <template #title>
            <span class="quickstart-icon">⚡</span>
            <span>快速开始</span>
          </template>
          <a-steps :current="currentStep" direction="vertical">
            <a-step title="安装依赖" description="运行 pnpm install 安装所有依赖" />
            <a-step title="启动开发环境" description="运行 pnpm dev:web 启动Web开发环境" />
            <a-step title="查看示例" description="访问示例页面了解框架使用方法" />
            <a-step title="开始开发" description="基于示例代码开始你的开发工作" />
          </a-steps>
        </a-card>
      </a-col>

      <!-- 系统信息 -->
      <a-col :xs="24" :sm="12">
        <a-card class="info-card">
          <template #title>
            <span class="info-icon">ℹ️</span>
            <span>环境信息</span>
          </template>
          <a-descriptions :column="1" bordered>
            <a-descriptions-item label="环境">{{ environment }}</a-descriptions-item>
            <a-descriptions-item label="平台">{{ platform }}</a-descriptions-item>
            <a-descriptions-item label="版本">{{ version }}</a-descriptions-item>
            <a-descriptions-item label="主题">{{ currentTheme }}</a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-col>

      <a-col :xs="24" :sm="12">
        <a-card class="info-card">
          <template #title>
            <span class="info-icon">🔧</span>
            <span>开发工具</span>
          </template>
          <a-list :data-source="devTools" size="small">
            <template #renderItem="{ item }">
              <a-list-item>
                <a-list-item-meta :title="item.title" :description="item.description" />
              </a-list-item>
            </template>
          </a-list>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { ServiceFactory } from 'core/src/services'

// 路由
const router = useRouter()

// 状态
const currentStep = ref(0)
const environment = ref('web')
const platform = ref('unknown')
const version = ref('1.0.0')
const currentTheme = ref('light')

// 开发工具列表
const devTools = [
  {
    title: 'Vue 3',
    description: '现代化的前端框架'
  },
  {
    title: 'TypeScript',
    description: '类型安全的JavaScript'
  },
  {
    title: 'Vite',
    description: '快速的构建工具'
  },
  {
    title: 'Ant Design Vue',
    description: '企业级UI组件库'
  },
  {
    title: 'Pinia',
    description: 'Vue状态管理'
  }
]

// 方法
const navigateToExamples = () => {
  router.push('/examples')
}

const navigateToAbout = () => {
  router.push('/about')
}

const getSystemInfo = async () => {
  try {
    const serviceFactory = ServiceFactory.getInstance()
    const systemService = await serviceFactory.getServiceManager().getSystem()
    const themeService = await serviceFactory.getServiceManager().getTheme()

    environment.value = await systemService.getEnvironment()
    platform.value = await systemService.getPlatform()
    currentTheme.value = await themeService.getTheme()
  } catch (error) {
    console.error('获取系统信息失败:', error)
  }
}

// 生命周期
onMounted(() => {
  getSystemInfo()

  // 模拟步骤进度
  setTimeout(() => {
    currentStep.value = 1
  }, 1000)

  setTimeout(() => {
    currentStep.value = 2
  }, 2000)

  setTimeout(() => {
    currentStep.value = 3
  }, 3000)
})
</script>

<style lang="less" scoped>
.home-container {
  max-width: 1200px;
  margin: 0 auto;
}

.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;

  .welcome-icon {
    text-align: center;

    .icon {
      font-size: 80px;
      display: block;
    }
  }

  .welcome-title {
    color: white;
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 16px;
  }

  .welcome-description {
    color: rgba(255, 255, 255, 0.9);
    font-size: 16px;
    margin-bottom: 24px;
    line-height: 1.6;
  }

  .welcome-actions {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
  }
}

.feature-card {
  height: 100%;
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-5px);
  }

  .feature-icon {
    margin-right: 8px;
  }
}

.quickstart-card {
  .quickstart-icon {
    margin-right: 8px;
  }
}

.info-card {
  .info-icon {
    margin-right: 8px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .welcome-card {
    .welcome-icon {
      .icon {
        font-size: 60px;
      }
    }

    .welcome-title {
      font-size: 24px;
    }

    .welcome-description {
      font-size: 14px;
    }
  }
}
</style>