<template>
  <div class="iframe-example">
    <a-page-header
      title="Iframe嵌入示例"
      subtitle="演示如何在iframe中嵌入应用"
      @back="() => router.push('/examples')"
    />

    <a-row :gutter="[24, 24]">
      <!-- iframe设置 -->
      <a-col :span="24">
        <a-card title="iframe设置">
          <a-form layout="vertical">
            <a-row :gutter="[16, 16]">
              <a-col :xs="24" :sm="8">
                <a-form-item label="iframe URL">
                  <a-input v-model:value="iframeSettings.url" placeholder="请输入iframe URL" />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="8">
                <a-form-item label="宽度">
                  <a-input-number
                    v-model:value="iframeSettings.width"
                    :min="200"
                    :max="1200"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="8">
                <a-form-item label="高度">
                  <a-input-number
                    v-model:value="iframeSettings.height"
                    :min="200"
                    :max="800"
                    style="width: 100%"
                  />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="6">
                <a-form-item label="显示边框">
                  <a-switch v-model:checked="iframeSettings.border" />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="6">
                <a-form-item label="允许滚动">
                  <a-switch v-model:checked="iframeSettings.scrolling" />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="6">
                <a-form-item label="允许全屏">
                  <a-switch v-model:checked="iframeSettings.allowFullscreen" />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="6">
                <a-form-item label="沙盒模式">
                  <a-switch v-model:checked="iframeSettings.sandbox" />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item>
                  <a-space>
                    <a-button type="primary" @click="updateIframe">更新iframe</a-button>
                    <a-button @click="resetSettings">重置设置</a-button>
                    <a-button @click="useTemplate('local')">本地页面</a-button>
                    <a-button @click="useTemplate('external')">外部网站</a-button>
                  </a-space>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>
      </a-col>

      <!-- iframe预览 -->
      <a-col :span="24">
        <a-card title="iframe预览">
          <div class="iframe-container">
            <iframe
              ref="iframeRef"
              :src="iframeSettings.url"
              :width="iframeSettings.width"
              :height="iframeSettings.height"
              :frameborder="iframeSettings.border ? '1' : '0'"
              :scrolling="iframeSettings.scrolling ? 'yes' : 'no'"
              :allowfullscreen="iframeSettings.allowFullscreen"
              :sandbox="iframeSettings.sandbox ? 'allow-scripts allow-same-origin' : undefined"
              @load="onIframeLoad"
              @error="onIframeError"
            ></iframe>
          </div>
          <div class="iframe-info">
            <a-descriptions :column="3" size="small">
              <a-descriptions-item label="URL">
                <a-typography-link :href="iframeSettings.url" target="_blank">
                  {{ iframeSettings.url }}
                </a-typography-link>
              </a-descriptions-item>
              <a-descriptions-item label="尺寸">
                {{ iframeSettings.width }} × {{ iframeSettings.height }}
              </a-descriptions-item>
              <a-descriptions-item label="加载状态">
                <a-tag :color="iframeLoaded ? 'green' : 'orange'">
                  {{ iframeLoaded ? '已加载' : '加载中' }}
                </a-tag>
              </a-descriptions-item>
            </a-descriptions>
          </div>
        </a-card>
      </a-col>

      <!-- 通信测试 -->
      <a-col :xs="24" :sm="12">
        <a-card title="通信测试">
          <a-form layout="vertical">
            <a-row :gutter="[16, 16]">
              <a-col :span="24">
                <a-form-item label="消息内容">
                  <a-input v-model:value="messageContent" placeholder="请输入要发送的消息" />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item>
                  <a-space>
                    <a-button type="primary" @click="sendMessage">发送消息</a-button>
                    <a-button @click="clearMessages">清空消息</a-button>
                  </a-space>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
          <div class="message-list">
            <h4>消息记录：</h4>
            <div v-if="messages.length === 0" class="empty-message">
              暂无消息记录
            </div>
            <div v-else>
              <div v-for="msg in messages" :key="msg.id" class="message-item">
                <div class="message-time">{{ msg.time }}</div>
                <div class="message-content">{{ msg.content }}</div>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>

      <!-- iframe API -->
      <a-col :xs="24" :sm="12">
        <a-card title="iframe API">
          <a-space direction="vertical" style="width: 100%">
            <a-button block @click="reloadIframe">重新加载</a-button>
            <a-button block @click="printIframe">打印iframe内容</a-button>
            <a-button block @click="getIframeContent">获取iframe内容</a-button>
            <a-button block @click="checkIframeSecurity">检查安全性</a-button>
          </a-space>
          <div class="api-result">
            <h4>API结果：</h4>
            <pre>{{ apiResult }}</pre>
          </div>
        </a-card>
      </a-col>

      <!-- 安全说明 -->
      <a-col :span="24">
        <a-card title="安全说明">
          <a-alert
            message="iframe安全性"
            description="iframe提供了一定的安全隔离，但仍需注意以下安全事项。"
            type="warning"
            show-icon
          />
          <div class="security-tips">
            <h4>安全建议：</h4>
            <a-list :data-source="securityTips" size="small">
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta :title="item.title" :description="item.description" />
                </a-list-item>
              </template>
            </a-list>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'

// 路由
const router = useRouter()

// iframe引用
const iframeRef = ref<HTMLIFrameElement>()

// iframe设置
const iframeSettings = reactive({
  url: 'https://example.com',
  width: 800,
  height: 600,
  border: true,
  scrolling: true,
  allowFullscreen: true,
  sandbox: true
})

// 状态
const iframeLoaded = ref(false)
const messageContent = ref('')
const messages = ref<Array<{ id: number; time: string; content: string }>>([])
const apiResult = ref('')

// 安全提示
const securityTips = [
  {
    title: '使用沙盒模式',
    description: '启用sandbox属性限制iframe的权限，防止恶意代码执行'
  },
  {
    title: '验证来源',
    description: '确保iframe加载的内容来自可信的来源'
  },
  {
    title: '限制权限',
    description: '根据需要限制iframe的权限，如脚本执行、表单提交等'
  },
  {
    title: '监控通信',
    description: '监控与iframe的通信，防止数据泄露'
  },
  {
    title: '避免敏感数据',
    description: '不要在iframe中处理敏感数据'
  }
]

// 更新iframe
const updateIframe = () => {
  iframeLoaded.value = false
  addHistory(`更新iframe: ${iframeSettings.url}`, 'info')
}

// 重置设置
const resetSettings = () => {
  Object.assign(iframeSettings, {
    url: 'https://example.com',
    width: 800,
    height: 600,
    border: true,
    scrolling: true,
    allowFullscreen: true,
    sandbox: true
  })
  message.success('设置已重置')
  addHistory('重置iframe设置', 'info')
}

// 使用模板
const useTemplate = (type: string) => {
  if (type === 'local') {
    iframeSettings.url = '/fullscreen'
  } else if (type === 'external') {
    iframeSettings.url = 'https://www.github.com'
  }
  updateIframe()
}

// iframe加载完成
const onIframeLoad = () => {
  iframeLoaded.value = true
  addHistory('iframe加载完成', 'success')
}

// iframe加载错误
const onIframeError = () => {
  iframeLoaded.value = false
  addHistory('iframe加载失败', 'error')
}

// 发送消息
const sendMessage = () => {
  if (!messageContent.value) {
    message.warning('请输入消息内容')
    return
  }

  try {
    if (iframeRef.value?.contentWindow) {
      iframeRef.value.contentWindow.postMessage(messageContent.value, '*')

      messages.value.unshift({
        id: Date.now(),
        time: new Date().toLocaleString(),
        content: `发送: ${messageContent.value}`
      })

      messageContent.value = ''
      addHistory('发送消息成功', 'success')
    } else {
      message.error('无法访问iframe内容')
      addHistory('发送消息失败: 无法访问iframe', 'error')
    }
  } catch (error) {
    message.error('发送消息失败')
    addHistory('发送消息失败', 'error')
  }
}

// 清空消息
const clearMessages = () => {
  messages.value = []
  addHistory('清空消息记录', 'info')
}

// 重新加载iframe
const reloadIframe = () => {
  if (iframeRef.value) {
    iframeRef.value.src = iframeRef.value.src
    addHistory('重新加载iframe', 'info')
  }
}

// 打印iframe内容
const printIframe = () => {
  if (iframeRef.value?.contentWindow) {
    try {
      iframeRef.value.contentWindow.print()
      addHistory('打印iframe内容', 'success')
    } catch (error) {
      message.error('打印失败')
      addHistory('打印iframe内容失败', 'error')
    }
  }
}

// 获取iframe内容
const getIframeContent = () => {
  if (iframeRef.value?.contentDocument) {
    try {
      const content = iframeRef.value.contentDocument.body.innerText
      apiResult.value = content.substring(0, 500) + (content.length > 500 ? '...' : '')
      addHistory('获取iframe内容成功', 'success')
    } catch (error) {
      apiResult.value = '获取内容失败'
      addHistory('获取iframe内容失败', 'error')
    }
  }
}

// 检查安全性
const checkIframeSecurity = () => {
  const security = {
    sandbox: iframeSettings.sandbox,
    allowFullscreen: iframeSettings.allowFullscreen,
    sameOrigin: iframeRef.value?.contentWindow?.origin === window.origin,
    hasContent: !!iframeRef.value?.contentDocument,
    canAccess: false
  }

  try {
    // 尝试访问iframe内容
    if (iframeRef.value?.contentDocument) {
      security.canAccess = true
    }
  } catch (error) {
    security.canAccess = false
  }

  apiResult.value = JSON.stringify(security, null, 2)
  addHistory('检查iframe安全性', 'info')
}

// 添加历史记录
const addHistory = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
  console.log(`[Iframe] ${message}`)
}

// 初始化
onMounted(() => {
  // 监听来自iframe的消息
  window.addEventListener('message', (event) => {
    messages.value.unshift({
      id: Date.now(),
      time: new Date().toLocaleString(),
      content: `接收: ${event.data}`
    })
  })

  addHistory('iframe示例页面已加载', 'info')
})
</script>

<style lang="less" scoped>
.iframe-example {
  max-width: 1200px;
  margin: 0 auto;
}

.iframe-container {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;

  iframe {
    border: 1px solid #d9d9d9;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
}

.iframe-info {
  margin-top: 16px;
}

.message-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 12px;

  h4 {
    margin-bottom: 8px;
    color: #1890ff;
  }

  .empty-message {
    text-align: center;
    color: #999;
    padding: 20px;
  }

  .message-item {
    margin-bottom: 8px;
    padding: 8px;
    background: #f5f5f5;
    border-radius: 4px;

    .message-time {
      font-size: 12px;
      color: #666;
      margin-bottom: 4px;
    }

    .message-content {
      font-size: 14px;
    }
  }
}

.api-result {
  margin-top: 16px;

  h4 {
    margin-bottom: 8px;
    color: #1890ff;
  }

  pre {
    background: #f5f5f5;
    padding: 12px;
    border-radius: 4px;
    overflow-x: auto;
    max-height: 200px;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.5;
  }
}

.security-tips {
  margin-top: 16px;

  h4 {
    margin-bottom: 8px;
    color: #1890ff;
  }
}
</style>