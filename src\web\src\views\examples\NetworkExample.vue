<template>
  <div class="network-example">
    <a-page-header
      title="网络请求示例"
      subtitle="演示如何进行网络请求和数据获取"
      @back="() => router.push('/examples')"
    />

    <a-row :gutter="[24, 24]">
      <!-- 请求设置 -->
      <a-col :span="24">
        <a-card title="请求设置">
          <a-form layout="vertical">
            <a-row :gutter="[16, 16]">
              <a-col :xs="24" :sm="12">
                <a-form-item label="请求URL">
                  <a-input v-model:value="requestOptions.url" placeholder="请输入请求URL" />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="6">
                <a-form-item label="请求方法">
                  <a-select v-model:value="requestOptions.method" style="width: 100%">
                    <a-select-option value="GET">GET</a-select-option>
                    <a-select-option value="POST">POST</a-select-option>
                    <a-select-option value="PUT">PUT</a-select-option>
                    <a-select-option value="DELETE">DELETE</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="6">
                <a-form-item label="超时时间(ms)">
                  <a-input-number v-model:value="requestOptions.timeout" :min="1000" :max="30000" style="width: 100%" />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="请求头">
                  <a-textarea
                    v-model:value="requestHeaders"
                    :rows="3"
                    placeholder="请输入请求头，格式：key:value&#10;例如：&#10;Content-Type: application/json&#10;Authorization: Bearer token"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item label="请求体">
                  <a-textarea
                    v-model:value="requestBody"
                    :rows="4"
                    placeholder="请输入请求体（POST/PUT请求时有效）"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item>
                  <a-space>
                    <a-button type="primary" :loading="loading" @click="sendRequest">
                      发送请求
                    </a-button>
                    <a-button @click="resetForm">重置</a-button>
                    <a-button @click="useTemplate('json')">使用JSON模板</a-button>
                    <a-button @click="useTemplate('form')">使用表单模板</a-button>
                  </a-space>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>
      </a-col>

      <!-- 响应结果 -->
      <a-col :span="24">
        <a-card title="响应结果">
          <a-tabs v-model:activeKey="responseTab">
            <a-tab-pane key="response" tab="响应">
              <div class="response-container">
                <div v-if="response" class="response-content">
                  <a-descriptions :column="2" bordered>
                    <a-descriptions-item label="状态码">
                      <a-tag :color="getStatusColor(response.status)">
                        {{ response.status }} {{ response.statusText }}
                      </a-tag>
                    </a-descriptions-item>
                    <a-descriptions-item label="响应时间">
                      {{ responseTime }}ms
                    </a-descriptions-item>
                  </a-descriptions>
                  <div class="response-data">
                    <h4>响应数据：</h4>
                    <pre>{{ formatResponseData(response.data) }}</pre>
                  </div>
                </div>
                <a-empty v-else description="暂无响应数据" />
              </div>
            </a-tab-pane>
            <a-tab-pane key="headers" tab="响应头">
              <div class="headers-container">
                <div v-if="response" class="headers-content">
                  <a-table
                    :columns="headerColumns"
                    :data-source="formatHeaders(response.headers)"
                    :pagination="false"
                    size="small"
                  />
                </div>
                <a-empty v-else description="暂无响应头信息" />
              </div>
            </a-tab-pane>
          </a-tabs>
        </a-card>
      </a-col>

      <!-- 请求历史 -->
      <a-col :span="24">
        <a-card title="请求历史">
          <a-table
            :columns="historyColumns"
            :data-source="requestHistory"
            :pagination="{ pageSize: 10 }"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'url'">
                <a-typography-text :ellipsis="true" :content="record.url" style="max-width: 300px" />
              </template>
              <template v-if="column.key === 'status'">
                <a-tag :color="getStatusColor(record.status)">
                  {{ record.status }}
                </a-tag>
              </template>
              <template v-if="column.key === 'actions'">
                <a-button type="link" size="small" @click="viewHistoryRecord(record)">
                  查看
                </a-button>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>

      <!-- 快速测试 -->
      <a-col :span="24">
        <a-card title="快速测试">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="6">
              <a-button block @click="quickTest('get')">
                测试GET请求
              </a-button>
            </a-col>
            <a-col :xs="24" :sm="6">
              <a-button block @click="quickTest('post')">
                测试POST请求
              </a-button>
            </a-col>
            <a-col :xs="24" :sm="6">
              <a-button block @click="quickTest('timeout')">
                测试超时
              </a-button>
            </a-col>
            <a-col :xs="24" :sm="6">
              <a-button block @click="quickTest('error')">
                测试错误
              </a-button>
            </a-col>
          </a-row>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'

// 路由
const router = useRouter()

// 请求选项
const requestOptions = reactive({
  url: 'https://jsonplaceholder.typicode.com/posts/1',
  method: 'GET',
  timeout: 5000
})

// 请求头和请求体
const requestHeaders = ref('')
const requestBody = ref('')

// 响应数据
const response = ref<any>(null)
const responseTime = ref(0)
const responseTab = ref('response')

// 加载状态
const loading = ref(false)

// 请求历史
const requestHistory = ref<Array<{
  id: string
  url: string
  method: string
  status: number
  time: string
  responseTime: number
}>>([])

// 表格列
const headerColumns = [
  {
    title: '头名称',
    dataIndex: 'key',
    key: 'key'
  },
  {
    title: '头值',
    dataIndex: 'value',
    key: 'value',
    ellipsis: true
  }
]

const historyColumns = [
  {
    title: '方法',
    dataIndex: 'method',
    key: 'method',
    width: 80
  },
  {
    title: 'URL',
    dataIndex: 'url',
    key: 'url'
  },
  {
    title: '状态码',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '响应时间',
    dataIndex: 'responseTime',
    key: 'responseTime',
    width: 100
  },
  {
    title: '请求时间',
    dataIndex: 'time',
    key: 'time',
    width: 160
  },
  {
    title: '操作',
    key: 'actions',
    width: 80,
    fixed: 'right'
  }
]

// 获取网络服务
const getNetworkService = async () => {
  try {
    const { ServiceFactory } = await import('core/src/services')
    const serviceFactory = ServiceFactory.getInstance()
    return await serviceFactory.getServiceManager().getNetwork()
  } catch (error) {
    console.error('获取网络服务失败:', error)
    message.error('获取网络服务失败')
    return null
  }
}

// 发送请求
const sendRequest = async () => {
  if (!requestOptions.url) {
    message.warning('请输入请求URL')
    return
  }

  loading.value = true
  response.value = null
  responseTime.value = 0

  try {
    const networkService = await getNetworkService()
    if (!networkService) return

    // 解析请求头
    const headers: Record<string, string> = {}
    if (requestHeaders.value) {
      const headerLines = requestHeaders.value.split('\n')
      for (const line of headerLines) {
        const [key, value] = line.split(':').map(s => s.trim())
        if (key && value) {
          headers[key] = value
        }
      }
    }

    // 准备请求数据
    const requestData = {
      url: requestOptions.url,
      method: requestOptions.method,
      headers,
      timeout: requestOptions.timeout
    }

    // 如果是POST或PUT请求，添加请求体
    if (['POST', 'PUT'].includes(requestOptions.method) && requestBody.value) {
      ;(requestData as any).data = requestBody.value
    }

    const startTime = Date.now()
    const result = await networkService.request(requestData)
    responseTime.value = Date.now() - startTime

    response.value = result

    // 添加到历史记录
    requestHistory.value.unshift({
      id: `request_${Date.now()}`,
      url: requestOptions.url,
      method: requestOptions.method,
      status: result.status,
      time: new Date().toLocaleString(),
      responseTime: responseTime.value
    })

    // 最多保留20条记录
    if (requestHistory.value.length > 20) {
      requestHistory.value = requestHistory.value.slice(0, 20)
    }

    message.success('请求发送成功')
  } catch (error) {
    console.error('请求失败:', error)
    response.value = {
      status: 0,
      statusText: '请求失败',
      headers: {},
      data: error instanceof Error ? error.message : '未知错误'
    }
    message.error('请求发送失败')
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  requestOptions.url = 'https://jsonplaceholder.typicode.com/posts/1'
  requestOptions.method = 'GET'
  requestOptions.timeout = 5000
  requestHeaders.value = ''
  requestBody.value = ''
  response.value = null
  responseTime.value = 0
}

// 使用模板
const useTemplate = (type: string) => {
  if (type === 'json') {
    requestOptions.url = 'https://jsonplaceholder.typicode.com/posts'
    requestOptions.method = 'POST'
    requestHeaders.value = 'Content-Type: application/json'
    requestBody.value = JSON.stringify({
      title: '测试标题',
      body: '测试内容',
      userId: 1
    }, null, 2)
  } else if (type === 'form') {
    requestOptions.url = 'https://jsonplaceholder.typicode.com/posts'
    requestOptions.method = 'POST'
    requestHeaders.value = 'Content-Type: application/x-www-form-urlencoded'
    requestBody.value = 'title=测试标题&body=测试内容&userId=1'
  }
}

// 快速测试
const quickTest = async (type: string) => {
  switch (type) {
    case 'get':
      requestOptions.url = 'https://jsonplaceholder.typicode.com/posts/1'
      requestOptions.method = 'GET'
      break
    case 'post':
      requestOptions.url = 'https://jsonplaceholder.typicode.com/posts'
      requestOptions.method = 'POST'
      requestHeaders.value = 'Content-Type: application/json'
      requestBody.value = JSON.stringify({
        title: '快速测试',
        body: '这是一个快速测试',
        userId: 1
      })
      break
    case 'timeout':
      requestOptions.url = 'https://httpstat.us/200?sleep=3000'
      requestOptions.timeout = 1000
      break
    case 'error':
      requestOptions.url = 'https://httpstat.us/404'
      break
  }
  await sendRequest()
}

// 查看历史记录
const viewHistoryRecord = (record: any) => {
  requestOptions.url = record.url
  requestOptions.method = record.method
}

// 格式化响应数据
const formatResponseData = (data: any) => {
  if (typeof data === 'string') {
    try {
      return JSON.stringify(JSON.parse(data), null, 2)
    } catch {
      return data
    }
  }
  return JSON.stringify(data, null, 2)
}

// 格式化响应头
const formatHeaders = (headers: Record<string, string>) => {
  return Object.entries(headers).map(([key, value]) => ({
    key,
    value
  }))
}

// 获取状态颜色
const getStatusColor = (status: number) => {
  if (status >= 200 && status < 300) return 'green'
  if (status >= 300 && status < 400) return 'orange'
  if (status >= 400 && status < 500) return 'red'
  return 'default'
}

// 初始化
onMounted(() => {
  console.log('网络请求示例页面已加载')
})
</script>

<style lang="less" scoped>
.network-example {
  max-width: 1200px;
  margin: 0 auto;
}

.response-container {
  .response-content {
    .response-data {
      margin-top: 16px;

      h4 {
        margin-bottom: 8px;
        color: #1890ff;
      }

      pre {
        background: #f5f5f5;
        padding: 12px;
        border-radius: 4px;
        overflow-x: auto;
        max-height: 400px;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
        font-size: 12px;
        line-height: 1.5;
      }
    }
  }
}

.headers-container {
  .headers-content {
    max-height: 400px;
    overflow-y: auto;
  }
}
</style>