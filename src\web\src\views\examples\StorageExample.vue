<template>
  <div class="storage-example">
    <a-page-header
      title="存储服务示例"
      subtitle="演示如何使用存储服务进行数据的保存和读取"
      @back="() => router.push('/examples')"
    />

    <a-row :gutter="[24, 24]">
      <!-- 基础操作 -->
      <a-col :span="24">
        <a-card title="基础操作">
          <a-form layout="vertical">
            <a-row :gutter="[16, 16]">
              <a-col :span="12">
                <a-form-item label="键名">
                  <a-input v-model:value="form.key" placeholder="请输入键名" />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="值">
                  <a-input v-model:value="form.value" placeholder="请输入值" />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item>
                  <a-space>
                    <a-button type="primary" @click="saveData">保存</a-button>
                    <a-button @click="getData">读取</a-button>
                    <a-button danger @click="removeData">删除</a-button>
                    <a-button @click="clearAll">清空所有</a-button>
                  </a-space>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>
      </a-col>

      <!-- 当前值显示 -->
      <a-col :span="24">
        <a-card title="当前值">
          <a-descriptions :column="1" bordered>
            <a-descriptions-item label="键名">{{ currentData.key || '-' }}</a-descriptions-item>
            <a-descriptions-item label="值">{{ currentData.value || '-' }}</a-descriptions-item>
            <a-descriptions-item label="数据类型">{{ currentData.type || '-' }}</a-descriptions-item>
            <a-descriptions-item label="存储时间">{{ currentData.timestamp || '-' }}</a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-col>

      <!-- 存储列表 -->
      <a-col :span="24">
        <a-card title="存储列表">
          <a-table
            :columns="columns"
            :data-source="storageList"
            :pagination="{ pageSize: 10 }"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'actions'">
                <a-space>
                  <a-button type="link" size="small" @click="loadItem(record.key)">
                    读取
                  </a-button>
                  <a-button type="link" size="small" danger @click="deleteItem(record.key)">
                    删除
                  </a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>

      <!-- 操作日志 -->
      <a-col :span="24">
        <a-card title="操作日志">
          <a-timeline>
            <a-timeline-item v-for="log in logs" :key="log.id" :color="log.color">
              <template #dot><component :is="log.icon" /></template>
              <div class="log-content">
                <div class="log-message">{{ log.message }}</div>
                <div class="log-time">{{ log.time }}</div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  SaveOutlined,
  ReadOutlined,
  DeleteOutlined,
  ClearOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue'

// 路由
const router = useRouter()

// 表单数据
const form = reactive({
  key: '',
  value: ''
})

// 当前数据
const currentData = ref({
  key: '',
  value: '',
  type: '',
  timestamp: ''
})

// 存储列表
const storageList = ref<Array<{ key: string; value: any; type: string; timestamp: string }>>([])

// 操作日志
const logs = ref<Array<{
  id: number
  message: string
  time: string
  color: string
  icon: any
}>>([])

// 表格列
const columns = [
  {
    title: '键名',
    dataIndex: 'key',
    key: 'key'
  },
  {
    title: '值',
    dataIndex: 'value',
    key: 'value',
    ellipsis: true
  },
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 100
  },
  {
    title: '存储时间',
    dataIndex: 'timestamp',
    key: 'timestamp',
    width: 160
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    fixed: 'right'
  }
]

// 获取存储服务
const getStorageService = async () => {
  try {
    const { ServiceFactory } = await import('core/src/services')
    const serviceFactory = ServiceFactory.getInstance()
    return await serviceFactory.getServiceManager().getStorage()
  } catch (error) {
    console.error('获取存储服务失败:', error)
    message.error('获取存储服务失败')
    return null
  }
}

// 添加日志
const addLog = (message: string, type: 'success' | 'error' | 'info' = 'info') => {
  const logMap = {
    success: { color: 'green', icon: CheckCircleOutlined },
    error: { color: 'red', icon: ExclamationCircleOutlined },
    info: { color: 'blue', icon: InfoCircleOutlined }
  }

  logs.value.unshift({
    id: Date.now(),
    message,
    time: new Date().toLocaleString(),
    color: logMap[type].color,
    icon: logMap[type].icon
  })

  // 最多保留50条日志
  if (logs.value.length > 50) {
    logs.value = logs.value.slice(0, 50)
  }
}

// 保存数据
const saveData = async () => {
  if (!form.key || !form.value) {
    message.warning('请输入键名和值')
    return
  }

  try {
    const storageService = await getStorageService()
    if (!storageService) return

    await storageService.setItem(form.key, form.value)
    message.success('数据保存成功')
    addLog(`保存数据: ${form.key} = ${form.value}`, 'success')
    await refreshList()
    form.value = ''
  } catch (error) {
    message.error('数据保存失败')
    addLog(`保存失败: ${form.key}`, 'error')
  }
}

// 读取数据
const getData = async () => {
  if (!form.key) {
    message.warning('请输入键名')
    return
  }

  try {
    const storageService = await getStorageService()
    if (!storageService) return

    const value = await storageService.getItem(form.key)
    if (value !== null) {
      currentData.value = {
        key: form.key,
        value: typeof value === 'object' ? JSON.stringify(value, null, 2) : value,
        type: typeof value,
        timestamp: new Date().toLocaleString()
      }
      message.success('数据读取成功')
      addLog(`读取数据: ${form.key}`, 'success')
    } else {
      currentData.value = {
        key: form.key,
        value: '',
        type: '',
        timestamp: ''
      }
      message.info('键名不存在')
      addLog(`读取失败: ${form.key} 不存在`, 'info')
    }
  } catch (error) {
    message.error('数据读取失败')
    addLog(`读取失败: ${form.key}`, 'error')
  }
}

// 删除数据
const removeData = async () => {
  if (!form.key) {
    message.warning('请输入键名')
    return
  }

  try {
    const storageService = await getStorageService()
    if (!storageService) return

    await storageService.removeItem(form.key)
    message.success('数据删除成功')
    addLog(`删除数据: ${form.key}`, 'success')
    await refreshList()
    form.key = ''
    form.value = ''
    currentData.value = {
      key: '',
      value: '',
      type: '',
      timestamp: ''
    }
  } catch (error) {
    message.error('数据删除失败')
    addLog(`删除失败: ${form.key}`, 'error')
  }
}

// 清空所有数据
const clearAll = async () => {
  try {
    const storageService = await getStorageService()
    if (!storageService) return

    await storageService.clear()
    message.success('所有数据已清空')
    addLog('清空所有数据', 'success')
    await refreshList()
    form.key = ''
    form.value = ''
    currentData.value = {
      key: '',
      value: '',
      type: '',
      timestamp: ''
    }
  } catch (error) {
    message.error('清空数据失败')
    addLog('清空所有数据失败', 'error')
  }
}

// 加载单个项目
const loadItem = async (key: string) => {
  form.key = key
  await getData()
}

// 删除单个项目
const deleteItem = async (key: string) => {
  form.key = key
  await removeData()
}

// 刷新列表
const refreshList = async () => {
  try {
    // 由于浏览器的安全限制，无法直接获取localStorage的所有键值对
    // 这里我们使用已知的一些键来演示
    const demoKeys = ['user', 'theme', 'settings', 'cache', 'lastVisit']
    const list = []

    for (const key of demoKeys) {
      try {
        const storageService = await getStorageService()
        if (!storageService) continue

        const value = await storageService.getItem(key)
        if (value !== null) {
          list.push({
            key,
            value: typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value),
            type: typeof value,
            timestamp: new Date().toLocaleString()
          })
        }
      } catch (error) {
        console.error(`读取 ${key} 失败:`, error)
      }
    }

    storageList.value = list
  } catch (error) {
    console.error('刷新列表失败:', error)
  }
}

// 初始化
onMounted(() => {
  refreshList()
  addLog('存储服务示例页面已加载', 'info')
})
</script>

<style lang="less" scoped>
.storage-example {
  max-width: 1200px;
  margin: 0 auto;
}

.log-content {
  .log-message {
    font-weight: 500;
    margin-bottom: 4px;
  }

  .log-time {
    font-size: 12px;
    color: #666;
  }
}
</style>