<template>
  <div class="theme-example">
    <a-page-header
      title="主题系统示例"
      subtitle="演示如何实现主题切换功能"
      @back="() => router.push('/examples')"
    />

    <a-row :gutter="[24, 24]">
      <!-- 主题切换 -->
      <a-col :span="24">
        <a-card title="主题切换">
          <a-row :gutter="[16, 16]">
            <a-col :xs="24" :sm="8">
              <a-card :class="['theme-card', 'light-theme']" :bordered="false">
                <template #cover>
                  <div class="theme-preview light-preview">
                    <div class="preview-header">
                      <div class="preview-title">浅色主题</div>
                    </div>
                    <div class="preview-content">
                      <div class="preview-item"></div>
                      <div class="preview-item"></div>
                      <div class="preview-item"></div>
                    </div>
                  </div>
                </template>
                <a-card-meta title="浅色主题">
                  <template #description>
                    <a-button
                      type="primary"
                      :ghost="currentTheme !== 'light'"
                      @click="setTheme('light')"
                      style="width: 100%"
                    >
                      {{ currentTheme === 'light' ? '当前主题' : '应用主题' }}
                    </a-button>
                  </template>
                </a-card-meta>
              </a-card>
            </a-col>

            <a-col :xs="24" :sm="8">
              <a-card :class="['theme-card', 'dark-theme']" :bordered="false">
                <template #cover>
                  <div class="theme-preview dark-preview">
                    <div class="preview-header">
                      <div class="preview-title">深色主题</div>
                    </div>
                    <div class="preview-content">
                      <div class="preview-item"></div>
                      <div class="preview-item"></div>
                      <div class="preview-item"></div>
                    </div>
                  </div>
                </template>
                <a-card-meta title="深色主题">
                  <template #description>
                    <a-button
                      type="primary"
                      :ghost="currentTheme !== 'dark'"
                      @click="setTheme('dark')"
                      style="width: 100%"
                    >
                      {{ currentTheme === 'dark' ? '当前主题' : '应用主题' }}
                    </a-button>
                  </template>
                </a-card-meta>
              </a-card>
            </a-col>

            <a-col :xs="24" :sm="8">
              <a-card :class="['theme-card', 'system-theme']" :bordered="false">
                <template #cover>
                  <div class="theme-preview system-preview">
                    <div class="preview-header">
                      <div class="preview-title">系统主题</div>
                    </div>
                    <div class="preview-content">
                      <div class="preview-item"></div>
                      <div class="preview-item"></div>
                      <div class="preview-item"></div>
                    </div>
                  </div>
                </template>
                <a-card-meta title="系统主题">
                  <template #description>
                    <a-button
                      type="primary"
                      :ghost="currentTheme !== 'system'"
                      @click="setTheme('system')"
                      style="width: 100%"
                    >
                      {{ currentTheme === 'system' ? '当前主题' : '应用主题' }}
                    </a-button>
                  </template>
                </a-card-meta>
              </a-card>
            </a-col>
          </a-row>
        </a-card>
      </a-col>

      <!-- 主题状态 -->
      <a-col :xs="24" :sm="12">
        <a-card title="主题状态">
          <a-descriptions :column="1" bordered>
            <a-descriptions-item label="当前主题">
              <a-tag :color="getThemeColor(currentTheme)">
                {{ getThemeText(currentTheme) }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="系统主题">
              <a-tag color="blue">
                {{ systemTheme }}
              </a-tag>
            </a-descriptions-item>
            <a-descriptions-item label="主题变化次数">
              {{ themeChangeCount }}
            </a-descriptions-item>
            <a-descriptions-item label="最后变化时间">
              {{ lastChangeTime }}
            </a-descriptions-item>
          </a-descriptions>
        </a-card>
      </a-col>

      <!-- 主题预览 -->
      <a-col :xs="24" :sm="12">
        <a-card title="组件预览">
          <div class="component-preview">
            <a-space direction="vertical" style="width: 100%">
              <a-button type="primary">主要按钮</a-button>
              <a-button>默认按钮</a-button>
              <a-input placeholder="输入框示例" />
              <a-select placeholder="选择框示例" style="width: 100%">
                <a-select-option value="1">选项1</a-select-option>
                <a-select-option value="2">选项2</a-select-option>
              </a-select>
              <a-card size="small" title="卡片示例">
                <p>这是一个卡片组件的示例</p>
              </a-card>
            </a-space>
          </div>
        </a-card>
      </a-col>

      <!-- 主题变化历史 -->
      <a-col :span="24">
        <a-card title="主题变化历史">
          <a-timeline>
            <a-timeline-item v-for="log in themeHistory" :key="log.id" :color="log.color">
              <template #dot><component :is="log.icon" /></template>
              <div class="log-content">
                <div class="log-message">{{ log.message }}</div>
                <div class="log-time">{{ log.time }}</div>
              </div>
            </a-timeline-item>
          </a-timeline>
        </a-card>
      </a-col>

      <!-- 使用说明 -->
      <a-col :span="24">
        <a-card title="使用说明">
          <a-alert
            message="主题系统说明"
            description="主题系统通过CSS变量和JavaScript事件监听实现，支持浅色、深色和系统主题三种模式。"
            type="info"
            show-icon
          />
          <div class="usage-tips">
            <h4>基本用法：</h4>
            <pre><code>// 获取主题服务
const themeService = await serviceFactory.getServiceManager().getTheme()

// 设置主题
await themeService.setTheme('dark')

// 获取当前主题
const theme = await themeService.getTheme()

// 监听主题变化
themeService.onThemeChange((newTheme) => {
  console.log('主题变化:', newTheme)
})</code></pre>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  SunOutlined,
  MoonOutlined,
  DesktopOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined
} from '@ant-design/icons-vue'

// 路由
const router = useRouter()

// 状态
const currentTheme = ref('light')
const systemTheme = ref('light')
const themeChangeCount = ref(0)
const lastChangeTime = ref('-')

// 主题历史
const themeHistory = ref<Array<{
  id: number
  message: string
  time: string
  color: string
  icon: any
}>>([])

// 监听系统主题变化
let mediaQueryListener: ((event: MediaQueryListEvent) => void) | null = null

// 获取主题服务
const getThemeService = async () => {
  try {
    const { ServiceFactory } = await import('core/src/services')
    const serviceFactory = ServiceFactory.getInstance()
    return await serviceFactory.getServiceManager().getTheme()
  } catch (error) {
    console.error('获取主题服务失败:', error)
    message.error('获取主题服务失败')
    return null
  }
}

// 设置主题
const setTheme = async (theme: 'light' | 'dark' | 'system') => {
  try {
    const themeService = await getThemeService()
    if (!themeService) return

    await themeService.setTheme(theme)
    currentTheme.value = theme
    themeChangeCount.value++
    lastChangeTime.value = new Date().toLocaleString()

    message.success(`已切换到${getThemeText(theme)}主题`)
    addHistory(`切换到${getThemeText(theme)}主题`, 'success')
  } catch (error) {
    message.error('主题切换失败')
    addHistory('主题切换失败', 'error')
  }
}

// 获取主题文本
const getThemeText = (theme: string) => {
  const themeMap = {
    light: '浅色主题',
    dark: '深色主题',
    system: '系统主题'
  }
  return themeMap[theme] || theme
}

// 获取主题颜色
const getThemeColor = (theme: string) => {
  const colorMap = {
    light: 'orange',
    dark: 'blue',
    system: 'green'
  }
  return colorMap[theme] || 'default'
}

// 添加历史记录
const addHistory = (message: string, type: 'success' | 'error' | 'info') => {
  const typeMap = {
    success: { color: 'green', icon: CheckCircleOutlined },
    error: { color: 'red', icon: InfoCircleOutlined },
    info: { color: 'blue', icon: InfoCircleOutlined }
  }

  themeHistory.value.unshift({
    id: Date.now(),
    message,
    time: new Date().toLocaleString(),
    color: typeMap[type].color,
    icon: typeMap[type].icon
  })

  // 最多保留50条记录
  if (themeHistory.value.length > 50) {
    themeHistory.value = themeHistory.value.slice(0, 50)
  }
}

// 检测系统主题
const checkSystemTheme = () => {
  const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches
  systemTheme.value = isDark ? 'dark' : 'light'
}

// 监听系统主题变化
const setupSystemThemeListener = () => {
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')

  mediaQueryListener = (event: MediaQueryListEvent) => {
    systemTheme.value = event.matches ? 'dark' : 'light'
    addHistory(`系统主题变化: ${systemTheme.value}`, 'info')
  }

  mediaQuery.addEventListener('change', mediaQueryListener)
}

// 初始化主题
const initTheme = async () => {
  try {
    const themeService = await getThemeService()
    if (!themeService) return

    // 获取当前主题
    const theme = await themeService.getTheme()
    currentTheme.value = theme

    // 监听主题变化
    themeService.onThemeChange((newTheme) => {
      currentTheme.value = newTheme
      themeChangeCount.value++
      lastChangeTime.value = new Date().toLocaleString()
      addHistory(`主题变化: ${getThemeText(newTheme)}`, 'info')
    })

    addHistory('主题系统初始化完成', 'success')
  } catch (error) {
    console.error('主题初始化失败:', error)
    addHistory('主题初始化失败', 'error')
  }
}

// 生命周期
onMounted(async () => {
  checkSystemTheme()
  setupSystemThemeListener()
  await initTheme()
})

onUnmounted(() => {
  // 清理事件监听
  if (mediaQueryListener) {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    mediaQuery.removeEventListener('change', mediaQueryListener)
  }
})
</script>

<style lang="less" scoped>
.theme-example {
  max-width: 1200px;
  margin: 0 auto;
}

.theme-card {
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .theme-preview {
    height: 120px;
    padding: 12px;
    border-radius: 8px 8px 0 0;

    .preview-header {
      padding: 8px;
      border-radius: 4px;
      margin-bottom: 8px;

      .preview-title {
        font-weight: 600;
        font-size: 14px;
      }
    }

    .preview-content {
      display: flex;
      gap: 8px;

      .preview-item {
        flex: 1;
        height: 20px;
        border-radius: 4px;
        opacity: 0.6;
      }
    }

    &.light-preview {
      background: #fff;
      border: 1px solid #f0f0f0;

      .preview-header {
        background: #1890ff;
        color: white;
      }

      .preview-item {
        background: #f5f5f5;
      }
    }

    &.dark-preview {
      background: #1f1f1f;
      border: 1px solid #303030;

      .preview-header {
        background: #40a9ff;
        color: white;
      }

      .preview-item {
        background: #303030;
      }
    }

    &.system-preview {
      background: linear-gradient(135deg, #fff 0%, #1f1f1f 100%);
      border: 1px solid #d9d9d9;

      .preview-header {
        background: #1890ff;
        color: white;
      }

      .preview-item {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }
}

.component-preview {
  padding: 16px;
  border-radius: 8px;
  background: var(--component-background, #fff);
  border: 1px solid var(--border-color, #f0f0f0);
}

.log-content {
  .log-message {
    font-weight: 500;
    margin-bottom: 4px;
  }

  .log-time {
    font-size: 12px;
    color: #666;
  }
}

.usage-tips {
  margin-top: 16px;

  h4 {
    margin-bottom: 8px;
    color: #1890ff;
  }

  pre {
    background: #f5f5f5;
    padding: 12px;
    border-radius: 4px;
    overflow-x: auto;

    code {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
      line-height: 1.5;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .theme-preview {
    height: 100px;
    padding: 8px;

    .preview-header {
      padding: 6px;
      margin-bottom: 6px;

      .preview-title {
        font-size: 12px;
      }
    }

    .preview-content {
      gap: 6px;

      .preview-item {
        height: 16px;
      }
    }
  }
}
</style>