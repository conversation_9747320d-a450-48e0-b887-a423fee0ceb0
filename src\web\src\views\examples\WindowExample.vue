<template>
  <div class="window-example">
    <a-page-header
      title="窗口管理示例"
      subtitle="演示如何创建和管理应用窗口"
      @back="() => router.push('/examples')"
    />

    <a-row :gutter="[24, 24]">
      <!-- 窗口操作 -->
      <a-col :span="24">
        <a-card title="窗口操作">
          <a-form layout="vertical">
            <a-row :gutter="[16, 16]">
              <a-col :xs="24" :sm="12" :md="6">
                <a-form-item label="窗口标题">
                  <a-input v-model:value="windowOptions.title" placeholder="请输入窗口标题" />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6">
                <a-form-item label="宽度">
                  <a-input-number v-model:value="windowOptions.width" :min="200" :max="1200" style="width: 100%" />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6">
                <a-form-item label="高度">
                  <a-input-number v-model:value="windowOptions.height" :min="200" :max="800" style="width: 100%" />
                </a-form-item>
              </a-col>
              <a-col :xs="24" :sm="12" :md="6">
                <a-form-item label="居中显示">
                  <a-switch v-model:checked="windowOptions.center" />
                </a-form-item>
              </a-col>
              <a-col :span="24">
                <a-form-item>
                  <a-space>
                    <a-button type="primary" @click="createWindow">创建窗口</a-button>
                    <a-button @click="closeAllWindows">关闭所有窗口</a-button>
                  </a-space>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </a-card>
      </a-col>

      <!-- 窗口列表 -->
      <a-col :span="24">
        <a-card title="已创建的窗口">
          <a-table
            :columns="windowColumns"
            :data-source="windowList"
            :pagination="{ pageSize: 10 }"
            size="small"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'actions'">
                <a-space>
                  <a-button type="link" size="small" @click="focusWindow(record.id)">
                    聚焦
                  </a-button>
                  <a-button type="link" size="small" @click="minimizeWindow(record.id)">
                    最小化
                  </a-button>
                  <a-button type="link" size="small" @click="maximizeWindow(record.id)">
                    最大化
                  </a-button>
                  <a-button type="link" size="small" danger @click="closeWindow(record.id)">
                    关闭
                  </a-button>
                </a-space>
              </template>
            </template>
          </a-table>
        </a-card>
      </a-col>

      <!-- 使用说明 -->
      <a-col :span="24">
        <a-card title="使用说明">
          <a-alert
            message="Web端限制"
            description="由于浏览器的安全限制，Web端的窗口功能受到一定限制。在实际的Desktop端，您可以创建功能更丰富的窗口。"
            type="info"
            show-icon
          />
          <div class="usage-tips">
            <h4>基本用法：</h4>
            <pre><code>// 创建窗口
const windowService = await serviceFactory.getServiceManager().getWindow()
const window = await windowService.createWindow({
  title: '新窗口',
  width: 800,
  height: 600,
  center: true
})

// 关闭窗口
await window.close()

// 聚焦窗口
await window.focus()</code></pre>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'

// 路由
const router = useRouter()

// 窗口选项
const windowOptions = reactive({
  title: '新窗口',
  width: 800,
  height: 600,
  center: true
})

// 窗口列表
const windowList = ref<Array<{
  id: string
  title: string
  width: number
  height: number
  status: string
  createTime: string
}>>([])

// 表格列
const windowColumns = [
  {
    title: '窗口ID',
    dataIndex: 'id',
    key: 'id',
    ellipsis: true
  },
  {
    title: '标题',
    dataIndex: 'title',
    key: 'title'
  },
  {
    title: '尺寸',
    dataIndex: 'size',
    key: 'size',
    width: 120
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 160
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right'
  }
]

// 获取窗口服务
const getWindowService = async () => {
  try {
    const { ServiceFactory } = await import('core/src/services')
    const serviceFactory = ServiceFactory.getInstance()
    return await serviceFactory.getServiceManager().getWindow()
  } catch (error) {
    console.error('获取窗口服务失败:', error)
    message.error('获取窗口服务失败')
    return null
  }
}

// 创建窗口
const createWindow = async () => {
  try {
    const windowService = await getWindowService()
    if (!windowService) return

    const window = await windowService.createWindow(windowOptions)

    const newWindow = {
      id: `window_${Date.now()}`,
      title: windowOptions.title,
      width: windowOptions.width,
      height: windowOptions.height,
      size: `${windowOptions.width}×${windowOptions.height}`,
      status: '已创建',
      createTime: new Date().toLocaleString()
    }

    windowList.value.push(newWindow)
    message.success('窗口创建成功')
  } catch (error) {
    message.error('窗口创建失败')
    console.error('窗口创建失败:', error)
  }
}

// 关闭窗口
const closeWindow = async (id: string) => {
  try {
    const windowService = await getWindowService()
    if (!windowService) return

    await windowService.closeWindow(id)
    windowList.value = windowList.value.filter(window => window.id !== id)
    message.success('窗口关闭成功')
  } catch (error) {
    message.error('窗口关闭失败')
    console.error('窗口关闭失败:', error)
  }
}

// 关闭所有窗口
const closeAllWindows = async () => {
  try {
    const windowService = await getWindowService()
    if (!windowService) return

    for (const window of windowList.value) {
      await windowService.closeWindow(window.id)
    }

    windowList.value = []
    message.success('所有窗口已关闭')
  } catch (error) {
    message.error('关闭所有窗口失败')
    console.error('关闭所有窗口失败:', error)
  }
}

// 聚焦窗口
const focusWindow = async (id: string) => {
  try {
    const window = windowList.value.find(w => w.id === id)
    if (window) {
      window.status = '已聚焦'
      message.success('窗口已聚焦')
    }
  } catch (error) {
    message.error('聚焦窗口失败')
    console.error('聚焦窗口失败:', error)
  }
}

// 最小化窗口
const minimizeWindow = async (id: string) => {
  try {
    const window = windowList.value.find(w => w.id === id)
    if (window) {
      window.status = '已最小化'
      message.success('窗口已最小化')
    }
  } catch (error) {
    message.error('最小化窗口失败')
    console.error('最小化窗口失败:', error)
  }
}

// 最大化窗口
const maximizeWindow = async (id: string) => {
  try {
    const window = windowList.value.find(w => w.id === id)
    if (window) {
      window.status = '已最大化'
      message.success('窗口已最大化')
    }
  } catch (error) {
    message.error('最大化窗口失败')
    console.error('最大化窗口失败:', error)
  }
}

// 初始化
onMounted(() => {
  console.log('窗口管理示例页面已加载')
})
</script>

<style lang="less" scoped>
.window-example {
  max-width: 1200px;
  margin: 0 auto;
}

.usage-tips {
  margin-top: 16px;

  h4 {
    margin-bottom: 8px;
    color: #1890ff;
  }

  pre {
    background: #f5f5f5;
    padding: 12px;
    border-radius: 4px;
    overflow-x: auto;

    code {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 12px;
      line-height: 1.5;
    }
  }
}
</style>