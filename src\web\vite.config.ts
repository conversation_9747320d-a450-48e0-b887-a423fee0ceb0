/**
 * Vite配置文件
 */

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],

  // 项目根目录
  root: '.',

  // 构建配置
  build: {
    outDir: '../../dist/web',
    emptyOutDir: true,
    sourcemap: true,
    rollupOptions: {
      input: resolve(__dirname, 'index.html'),
      output: {
        manualChunks: {
          // 将node_modules中的依赖单独打包
          vendor: ['vue', 'vue-router', 'pinia', 'ant-design-vue'],
          // 将公共代码单独打包
          common: ['core', 'common']
        }
      }
    }
  },

  // 开发服务器配置
  server: {
    host: '0.0.0.0',
    port: 3000,
    open: true,
    cors: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },

  // 解析配置
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@common': resolve(__dirname, '../../src/common/src'),
      '@core': resolve(__dirname, '../../src/core/src'),
      '@web': resolve(__dirname, 'src')
    }
  },

  // CSS配置
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
        modifyVars: {
          '@primary-color': '#1890ff',
          '@border-radius-base': '4px',
          '@font-size-base': '14px',
          '@line-height-base': 1.5
        }
      }
    }
  },

  // 定义全局常量
  define: {
    __VUE_OPTIONS_API__: true,
    __VUE_PROD_DEVTOOLS__: false,
    __APP_ENV__: JSON.stringify(process.env.NODE_ENV || 'development')
  },

  // 优化配置
  optimizeDeps: {
    include: [
      'vue',
      'vue-router',
      'pinia',
      'ant-design-vue',
      '@ant-design/icons-vue'
    ]
  }
})