{"compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@common/*": ["src/common/src/*"], "@core/*": ["src/core/src/*"], "@web/*": ["src/web/src/*"], "@desktop/*": ["src/desktop/src/*"]}}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue"], "references": [{"path": "./tsconfig.node.json"}, {"path": "./src/common/tsconfig.json"}, {"path": "./src/core/tsconfig.json"}, {"path": "./src/web/tsconfig.json"}, {"path": "./src/desktop/tsconfig.json"}]}