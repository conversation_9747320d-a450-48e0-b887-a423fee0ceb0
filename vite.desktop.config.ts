/**
 * Desktop端Vite配置
 */

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// 导入基础配置
import baseConfig from './vite.config'

export default defineConfig(({ mode }) => {
  const isDevelopment = mode === 'development'
  const isProduction = mode === 'production'

  return {
    // 继承基础配置
    ...baseConfig,

    // 构建配置
    build: {
      ...baseConfig.build,
      // Desktop端输出目录
      outDir: 'dist/desktop/renderer',
      // 静态资源公共路径
      assetsDir: 'assets',
      // 相对路径（Electron需要）
      base: './',
      // 不生成manifest.json
      manifest: false
    },

    // 开发服务器配置
    server: {
      ...baseConfig.server,
      // Desktop端开发端口
      port: 3001,
      // 不自动打开浏览器（Electron会打开）
      open: false
    },

    // 插件配置
    plugins: [
      ...baseConfig.plugins,
      // Desktop端特有插件
      {
        name: 'desktop-config',
        configResolved(config) {
          // Desktop端配置处理
          config.define.__WEB__ = false
          config.define.__DESKTOP__ = true
        }
      }
    ],

    // 环境变量前缀
    envPrefix: 'VITE_DESKTOP_',

    // 定义全局变量
    define: {
      ...baseConfig.define,
      __PLATFORM__: JSON.stringify('desktop'),
      __ENVIRONMENT__: JSON.stringify(mode),
      __WEB__: false,
      __DESKTOP__: true
    },

    // CSS配置
    css: {
      ...baseConfig.css,
      preprocessorOptions: {
        less: {
          ...baseConfig.css?.preprocessorOptions?.less,
          // Desktop端特有变量
          additionalData: `@import "@/desktop/renderer/src/assets/styles/variables.less";`
        }
      }
    },

    // 路径解析
    resolve: {
      ...baseConfig.resolve,
      alias: {
        ...baseConfig.resolve?.alias,
        '@': resolve(__dirname, 'src/desktop/renderer/src'),
        '@common': resolve(__dirname, 'src/common/src'),
        '@core': resolve(__dirname, 'src/core/src')
      }
    },

    // 依赖优化
    optimizeDeps: {
      ...baseConfig.optimizeDeps,
      include: [
        ...baseConfig.optimizeDeps?.include || [],
        // Desktop端特有依赖
        '@electron-toolkit/preload'
      ]
    },

    // Electron特有配置
    electron: {
      // 主进程入口文件
      main: 'src/desktop/main/index.ts',
      // 预加载脚本
      preload: 'src/desktop/preload/index.ts',
      // 输出目录
      outDir: 'dist/desktop',
      // 资源目录
      resources: 'src/desktop/resources'
    }
  }
})