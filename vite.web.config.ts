/**
 * Web端Vite配置
 */

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// 导入基础配置
import baseConfig from './vite.config'

export default defineConfig(({ mode }) => {
  const isDevelopment = mode === 'development'
  const isProduction = mode === 'production'

  return {
    // 继承基础配置
    ...baseConfig,

    // 构建配置
    build: {
      ...baseConfig.build,
      // Web端输出目录
      outDir: 'dist/web',
      // 静态资源公共路径
      assetsDir: 'assets',
      // 生成manifest.json
      manifest: true
    },

    // 开发服务器配置
    server: {
      ...baseConfig.server,
      // Web端开发端口
      port: 3000,
      // 自动打开浏览器
      open: true,
      // 配置代理
      proxy: {
        '/api': {
          target: 'http://localhost:8080',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/api/, '')
        }
      }
    },

    // 插件配置
    plugins: [
      ...baseConfig.plugins,
      // Web端特有插件
      {
        name: 'web-config',
        configResolved(config) {
          // Web端配置处理
          config.define.__WEB__ = true
          config.define.__DESKTOP__ = false
        }
      }
    ],

    // 环境变量前缀
    envPrefix: 'VITE_WEB_',

    // 定义全局变量
    define: {
      ...baseConfig.define,
      __PLATFORM__: JSON.stringify('web'),
      __ENVIRONMENT__: JSON.stringify(mode),
      __WEB__: true,
      __DESKTOP__: false
    },

    // CSS配置
    css: {
      ...baseConfig.css,
      preprocessorOptions: {
        less: {
          ...baseConfig.css?.preprocessorOptions?.less,
          // Web端特有变量
          additionalData: `@import "@/web/src/assets/styles/variables.less";`
        }
      }
    },

    // 路径解析
    resolve: {
      ...baseConfig.resolve,
      alias: {
        ...baseConfig.resolve?.alias,
        '@': resolve(__dirname, 'src/web/src'),
        '@common': resolve(__dirname, 'src/common/src'),
        '@core': resolve(__dirname, 'src/core/src')
      }
    },

    // 依赖优化
    optimizeDeps: {
      ...baseConfig.optimizeDeps,
      include: [
        ...baseConfig.optimizeDeps?.include || [],
        // Web端特有依赖
        '@vueuse/core'
      ]
    }
  }
})